<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kaggle Assistant</title>
    <style>
        body {
            width: 300px;
            padding: 20px;
            margin: 0;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        
        .header p {
            margin: 5px 0 0 0;
            font-size: 12px;
            opacity: 0.8;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-dot.active {
            background: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }
        
        .status-dot.inactive {
            background: #ff6b6b;
        }
        
        .actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .action-btn {
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            text-align: center;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }
        
        .action-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 11px;
            opacity: 0.7;
        }
        
        .feature-list {
            font-size: 12px;
            margin: 10px 0;
        }
        
        .feature-list li {
            margin: 5px 0;
            list-style-type: none;
            padding-left: 15px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #4CAF50;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Kaggle Assistant</h1>
        <p>AI-powered notebook helper</p>
    </div>
    
    <div class="status">
        <div class="status-indicator">
            <div class="status-dot" id="status-dot"></div>
            <span id="status-text">Checking Kaggle page...</span>
        </div>
        <div class="feature-list">
            <li>Data analysis tools</li>
            <li>Code suggestions</li>
            <li>Visualization helpers</li>
            <li>Performance optimization</li>
        </div>
    </div>
    
    <div class="actions">
        <button class="action-btn" id="toggle-sidebar">Toggle Sidebar</button>
        <button class="action-btn" id="open-kaggle">Open Kaggle Notebooks</button>
        <button class="action-btn" id="refresh-extension">Refresh Extension</button>
    </div>
    
    <div class="footer">
        <p>Version 1.0.0</p>
        <p>Works only on Kaggle notebook pages</p>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
