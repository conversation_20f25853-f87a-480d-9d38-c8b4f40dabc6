// Content script for Kaggle Notebook Assistant
(function() {
    'use strict';

    // Prevent multiple injections
    if (window.kaggleAssistantLoaded) {
        console.log('🔄 Kaggle Assistant: Already loaded, skipping');
        return;
    }

    window.kaggleAssistantLoaded = true;
    console.log('🤖 Kaggle Assistant: Main content script loading...');

class KaggleAssistant {
  constructor() {
    this.sidebar = null;
    this.isVisible = false;
    this.currentCell = null;
    this.init();
  }

  init() {
    // Enhanced Kaggle detection
    if (!this.isKaggleNotebook()) {
      console.log('Kaggle Assistant: Not on a Kaggle notebook page');
      return;
    }

    console.log('Kaggle Assistant: Initializing on Kaggle notebook');
    this.waitForNotebookLoad().then(() => {
      this.createSidebar();
      this.setupEventListeners();
      this.observeNotebookChanges();
      this.detectNotebookType();
    });
  }

  isKaggleNotebook() {
    const url = window.location.href;
    const hostname = window.location.hostname;

    // Check if we're on Kaggle
    if (!hostname.includes('kaggle.com')) {
      return false;
    }

    // Check if we're on a notebook page
    if (url.includes('/code/') || url.includes('/notebook/')) {
      return true;
    }

    // Check for notebook editor elements
    const notebookIndicators = [
      '[data-testid="notebook-container"]',
      '.notebook-container',
      '.code-cell',
      '[data-testid="code-cell"]',
      '.cell-container'
    ];

    return notebookIndicators.some(selector => document.querySelector(selector));
  }

  async waitForNotebookLoad() {
    // Wait for notebook interface to load
    return new Promise((resolve) => {
      const checkForNotebook = () => {
        const notebookLoaded = document.querySelector('.notebook-container, [data-testid="notebook-container"], .code-cell');
        if (notebookLoaded) {
          resolve();
        } else {
          setTimeout(checkForNotebook, 500);
        }
      };
      checkForNotebook();
    });
  }

  detectNotebookType() {
    // Detect if it's a competition, dataset, or general notebook
    const url = window.location.href;
    let notebookType = 'general';

    if (url.includes('/competitions/')) {
      notebookType = 'competition';
    } else if (url.includes('/datasets/')) {
      notebookType = 'dataset';
    }

    this.notebookType = notebookType;
    this.updateSidebarForType(notebookType);
  }

  updateSidebarForType(type) {
    // Customize sidebar based on notebook type
    const typeSpecificSection = document.createElement('div');
    typeSpecificSection.className = 'section notebook-type-section';

    let typeContent = '';
    switch (type) {
      case 'competition':
        typeContent = `
          <h4>🏆 Competition Tools</h4>
          <button class="action-btn" id="competition-analysis">Analyze Competition Data</button>
          <button class="action-btn" id="submission-tips">Submission Tips</button>
          <button class="action-btn" id="leaderboard-strategy">Leaderboard Strategy</button>
        `;
        break;
      case 'dataset':
        typeContent = `
          <h4>📊 Dataset Tools</h4>
          <button class="action-btn" id="dataset-exploration">Explore Dataset</button>
          <button class="action-btn" id="data-quality">Data Quality Check</button>
          <button class="action-btn" id="feature-analysis">Feature Analysis</button>
        `;
        break;
      default:
        typeContent = `
          <h4>📝 Notebook Tools</h4>
          <button class="action-btn" id="notebook-structure">Improve Structure</button>
          <button class="action-btn" id="documentation">Add Documentation</button>
        `;
    }

    typeSpecificSection.innerHTML = typeContent;

    // Insert after the first section
    const firstSection = this.sidebar.querySelector('.section');
    if (firstSection && firstSection.nextSibling) {
      firstSection.parentNode.insertBefore(typeSpecificSection, firstSection.nextSibling);
    }

    // Add event listeners for new buttons
    this.setupTypeSpecificListeners(type);
  }

  createSidebar() {
    // Remove existing sidebar if present
    const existingSidebar = document.getElementById('kaggle-assistant-sidebar');
    if (existingSidebar) {
      existingSidebar.remove();
    }

    // Create sidebar container
    this.sidebar = document.createElement('div');
    this.sidebar.id = 'kaggle-assistant-sidebar';
    this.sidebar.className = 'kaggle-assistant-sidebar';
    
    // Create sidebar content with enhanced UI
    this.sidebar.innerHTML = `
      <div class="sidebar-header">
        <h3>🤖 Kaggle Assistant</h3>
        <div class="header-controls">
          <button id="minimize-sidebar" class="toggle-btn" title="Minimize">−</button>
          <button id="toggle-sidebar" class="toggle-btn" title="Close">×</button>
        </div>
      </div>
      <div class="sidebar-content">
        <div class="status-bar">
          <div class="status-indicator">
            <span class="status-dot active"></span>
            <span class="status-text">Ready</span>
          </div>
          <div class="notebook-info">
            <span id="cell-count">0 cells</span>
          </div>
        </div>

        <div class="section collapsible">
          <div class="section-header">
            <h4>📊 Analysis Tools</h4>
          </div>
          <div class="section-content">
            <button class="action-btn tooltip" id="analyze-data" data-tooltip="Analyze code patterns and complexity">
              <span class="btn-icon">🔍</span> Analyze Current Data
            </button>
            <button class="action-btn tooltip" id="suggest-viz" data-tooltip="Get visualization recommendations">
              <span class="btn-icon">📈</span> Suggest Visualizations
            </button>
            <button class="action-btn tooltip" id="data-summary" data-tooltip="Generate comprehensive data summary">
              <span class="btn-icon">📋</span> Data Summary
            </button>
            <button class="action-btn tooltip" id="analyze-all" data-tooltip="Analyze entire notebook">
              <span class="btn-icon">🔬</span> Analyze All Cells
            </button>
          </div>
        </div>

        <div class="section collapsible">
          <div class="section-header">
            <h4>💻 Code Helpers</h4>
          </div>
          <div class="section-content">
            <button class="action-btn tooltip" id="code-suggestions" data-tooltip="Get contextual code suggestions">
              <span class="btn-icon">💡</span> Get Code Suggestions
            </button>
            <button class="action-btn tooltip" id="optimize-code" data-tooltip="Optimize current cell performance">
              <span class="btn-icon">⚡</span> Optimize Current Cell
            </button>
            <button class="action-btn tooltip" id="add-comments" data-tooltip="Add meaningful comments">
              <span class="btn-icon">💬</span> Add Comments
            </button>
            <button class="action-btn tooltip" id="format-code" data-tooltip="Format and clean code">
              <span class="btn-icon">🎨</span> Format Code
            </button>
          </div>
        </div>

        <div class="section collapsible">
          <div class="section-header">
            <h4>🔍 Quick Actions</h4>
          </div>
          <div class="section-content">
            <button class="action-btn tooltip" id="common-imports" data-tooltip="Insert common data science imports">
              <span class="btn-icon">📦</span> Add Common Imports
            </button>
            <button class="action-btn tooltip" id="error-help" data-tooltip="Get help with current errors">
              <span class="btn-icon">🐛</span> Debug Current Error
            </button>
            <button class="action-btn tooltip" id="performance-tips" data-tooltip="Get performance optimization tips">
              <span class="btn-icon">🚀</span> Performance Tips
            </button>
            <button class="action-btn tooltip" id="export-code" data-tooltip="Export code templates">
              <span class="btn-icon">📤</span> Export Templates
            </button>
          </div>
        </div>

        <div class="section">
          <div class="section-header">
            <h4>📝 Results</h4>
            <button id="clear-results" class="clear-btn" title="Clear results">🗑️</button>
          </div>
          <div id="results-container" class="results-container">
            <div class="placeholder">
              <div class="placeholder-icon">🎯</div>
              <p>Select a tool above to see results here</p>
              <div class="quick-tips">
                <small>💡 Tip: Click on any code block to copy it</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add sidebar to page
    document.body.appendChild(this.sidebar);
    this.isVisible = true;
  }

  setupEventListeners() {
    // Header controls
    const toggleBtn = document.getElementById('toggle-sidebar');
    const minimizeBtn = document.getElementById('minimize-sidebar');
    const clearBtn = document.getElementById('clear-results');

    toggleBtn?.addEventListener('click', () => this.toggleSidebar());
    minimizeBtn?.addEventListener('click', () => this.minimizeSidebar());
    clearBtn?.addEventListener('click', () => this.clearResults());

    // Collapsible sections
    this.setupCollapsibleSections();

    // Analysis tools
    document.getElementById('analyze-data')?.addEventListener('click', () => this.analyzeData());
    document.getElementById('suggest-viz')?.addEventListener('click', () => this.suggestVisualizations());
    document.getElementById('data-summary')?.addEventListener('click', () => this.generateDataSummary());
    document.getElementById('analyze-all')?.addEventListener('click', () => this.analyzeAllCells());

    // Code helpers
    document.getElementById('code-suggestions')?.addEventListener('click', () => this.getCodeSuggestions());
    document.getElementById('optimize-code')?.addEventListener('click', () => this.optimizeCode());
    document.getElementById('add-comments')?.addEventListener('click', () => this.addComments());
    document.getElementById('format-code')?.addEventListener('click', () => this.formatCode());

    // Quick actions
    document.getElementById('common-imports')?.addEventListener('click', () => this.addCommonImports());
    document.getElementById('error-help')?.addEventListener('click', () => this.debugError());
    document.getElementById('performance-tips')?.addEventListener('click', () => this.showPerformanceTips());
    document.getElementById('export-code')?.addEventListener('click', () => this.exportTemplates());

    // Enhanced cell tracking
    this.setupCellTracking();

    // Keyboard shortcuts
    this.setupKeyboardShortcuts();

    // Update cell count
    this.updateCellCount();
  }

  setupCollapsibleSections() {
    const collapsibleSections = document.querySelectorAll('.section.collapsible .section-header');
    collapsibleSections.forEach(header => {
      header.addEventListener('click', () => {
        const section = header.closest('.section');
        section.classList.toggle('collapsed');
      });
    });
  }

  setupCellTracking() {
    // Enhanced cell selection tracking
    document.addEventListener('click', (e) => {
      const cell = e.target.closest('[data-testid="code-cell"], .code-cell, .cell');
      if (cell) {
        this.currentCell = cell;
        this.highlightActiveCell(cell);
        this.updateContextInfo();
      }
    });

    // Track focus changes
    document.addEventListener('focusin', (e) => {
      const cell = e.target.closest('[data-testid="code-cell"], .code-cell, .cell');
      if (cell) {
        this.currentCell = cell;
        this.updateContextInfo();
      }
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+K to toggle sidebar
      if (e.ctrlKey && e.shiftKey && e.key === 'K') {
        e.preventDefault();
        this.toggleSidebar();
      }

      // Ctrl+Shift+A to analyze current cell
      if (e.ctrlKey && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        this.analyzeData();
      }
    });
  }

  minimizeSidebar() {
    this.sidebar.classList.toggle('minimized');
    const content = this.sidebar.querySelector('.sidebar-content');
    if (this.sidebar.classList.contains('minimized')) {
      content.style.display = 'none';
      this.sidebar.style.width = '60px';
    } else {
      content.style.display = 'block';
      this.sidebar.style.width = '350px';
    }
  }

  clearResults() {
    const resultsContainer = document.getElementById('results-container');
    if (resultsContainer) {
      resultsContainer.innerHTML = `
        <div class="placeholder">
          <div class="placeholder-icon">🎯</div>
          <p>Results cleared. Select a tool above to see new results.</p>
        </div>
      `;
    }
  }

  highlightActiveCell(cell) {
    // Remove previous highlights
    document.querySelectorAll('.cell.assistant-active, .code-cell.assistant-active').forEach(c => {
      c.classList.remove('assistant-active');
    });

    // Add highlight to current cell
    cell.classList.add('assistant-active');

    // Add temporary visual feedback
    const indicator = document.createElement('div');
    indicator.className = 'cell-indicator';
    indicator.textContent = '🤖';
    indicator.style.cssText = `
      position: absolute;
      top: 5px;
      right: 5px;
      background: #4CAF50;
      color: white;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      z-index: 1000;
      animation: fadeInOut 2s ease-in-out;
    `;

    cell.style.position = 'relative';
    cell.appendChild(indicator);

    setTimeout(() => {
      indicator.remove();
    }, 2000);
  }

  updateCellCount() {
    const cellCount = document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]').length;
    const cellCountElement = document.getElementById('cell-count');
    if (cellCountElement) {
      cellCountElement.textContent = `${cellCount} cells`;
    }
  }

  updateContextInfo() {
    const statusText = document.querySelector('.status-text');
    if (statusText && this.currentCell) {
      const cellIndex = Array.from(document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]')).indexOf(this.currentCell) + 1;
      statusText.textContent = `Cell ${cellIndex} selected`;
    }
  }

  toggleSidebar() {
    if (this.isVisible) {
      this.sidebar.style.transform = 'translateX(100%)';
      this.isVisible = false;
    } else {
      this.sidebar.style.transform = 'translateX(0)';
      this.isVisible = true;
    }
  }

  getCurrentCellCode() {
    if (!this.currentCell) {
      // Enhanced cell detection for different Kaggle notebook versions
      this.currentCell = this.findActiveCell();
    }

    if (this.currentCell) {
      const code = this.extractCodeFromCell(this.currentCell);
      return code;
    }
    return '';
  }

  findActiveCell() {
    // Try multiple selectors for different Kaggle notebook layouts
    const cellSelectors = [
      '.cell.selected',
      '.code-cell.selected',
      '[data-testid="code-cell"].selected',
      '.cell.focused',
      '.code-cell.focused',
      '.cell:focus-within',
      '.code-cell:focus-within',
      // Fallback to first visible cell
      '.cell:not(.hidden)',
      '.code-cell:not(.hidden)',
      '[data-testid="code-cell"]:not(.hidden)'
    ];

    for (const selector of cellSelectors) {
      const cell = document.querySelector(selector);
      if (cell) {
        return cell;
      }
    }

    return null;
  }

  extractCodeFromCell(cell) {
    // Try multiple methods to extract code from different editor types
    const codeSelectors = [
      '.CodeMirror-code',
      '.monaco-editor .view-lines',
      '.ace_content',
      'textarea',
      '.input-area',
      '.code-input',
      '[data-testid="code-input"]'
    ];

    for (const selector of codeSelectors) {
      const codeElement = cell.querySelector(selector);
      if (codeElement) {
        // Handle different editor types
        if (codeElement.classList.contains('CodeMirror-code')) {
          return this.extractFromCodeMirror(codeElement);
        } else if (codeElement.classList.contains('view-lines')) {
          return this.extractFromMonaco(codeElement);
        } else if (codeElement.tagName === 'TEXTAREA') {
          return codeElement.value;
        } else {
          return codeElement.textContent || codeElement.innerText || '';
        }
      }
    }

    return '';
  }

  extractFromCodeMirror(element) {
    // Extract code from CodeMirror editor
    const lines = element.querySelectorAll('.CodeMirror-line');
    return Array.from(lines).map(line => line.textContent).join('\n');
  }

  extractFromMonaco(element) {
    // Extract code from Monaco editor
    const lines = element.querySelectorAll('.view-line');
    return Array.from(lines).map(line => line.textContent).join('\n');
  }

  getAllNotebookCode() {
    // Get code from all cells in the notebook
    const cells = document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]');
    const allCode = [];

    cells.forEach(cell => {
      const code = this.extractCodeFromCell(cell);
      if (code.trim()) {
        allCode.push(code);
      }
    });

    return allCode.join('\n\n');
  }

  getNotebookMetadata() {
    // Extract notebook metadata for better context
    const metadata = {
      title: document.title,
      url: window.location.href,
      cellCount: document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]').length,
      hasOutput: document.querySelectorAll('.output, .cell-output').length > 0,
      language: this.detectLanguage(),
      notebookType: this.notebookType || 'general'
    };

    return metadata;
  }

  detectLanguage() {
    // Detect the primary programming language used
    const allCode = this.getAllNotebookCode();

    if (allCode.includes('import pandas') || allCode.includes('import numpy') || allCode.includes('def ')) {
      return 'python';
    } else if (allCode.includes('library(') || allCode.includes('<-')) {
      return 'r';
    } else if (allCode.includes('SELECT') || allCode.includes('FROM')) {
      return 'sql';
    }

    return 'python'; // Default assumption for Kaggle
  }

  updateResults(content) {
    const resultsContainer = document.getElementById('results-container');
    if (resultsContainer) {
      resultsContainer.innerHTML = content;
    }
  }

  // Analysis methods
  analyzeData() {
    const code = this.getCurrentCellCode();
    this.updateResults('<div class="loading">Analyzing data...</div>');
    
    chrome.runtime.sendMessage({
      action: 'analyzeCode',
      code: code
    }, (response) => {
      if (response.success) {
        const analysis = response.analysis;
        this.updateResults(`
          <div class="analysis-result">
            <h5>Code Analysis</h5>
            <p><strong>Lines:</strong> ${analysis.complexity.lines}</p>
            <p><strong>Functions:</strong> ${analysis.complexity.functions}</p>
            <p><strong>Classes:</strong> ${analysis.complexity.classes}</p>
            <div class="suggestions">
              <h6>Suggestions:</h6>
              ${analysis.suggestions.map(s => `<li>${s}</li>`).join('')}
            </div>
            <div class="patterns">
              <h6>Detected Patterns:</h6>
              ${analysis.patterns.map(p => `<li>${p}</li>`).join('')}
            </div>
          </div>
        `);
      } else {
        this.updateResults('<div class="error">Error analyzing code</div>');
      }
    });
  }

  suggestVisualizations() {
    this.updateResults(`
      <div class="viz-suggestions">
        <h5>Visualization Suggestions</h5>
        <div class="suggestion-item">
          <strong>Histogram:</strong> For distribution analysis
          <code>plt.hist(data, bins=30)</code>
        </div>
        <div class="suggestion-item">
          <strong>Scatter Plot:</strong> For correlation analysis
          <code>plt.scatter(x, y)</code>
        </div>
        <div class="suggestion-item">
          <strong>Box Plot:</strong> For outlier detection
          <code>plt.boxplot(data)</code>
        </div>
        <div class="suggestion-item">
          <strong>Heatmap:</strong> For correlation matrix
          <code>sns.heatmap(df.corr(), annot=True)</code>
        </div>
      </div>
    `);
  }

  generateDataSummary() {
    this.updateResults(`
      <div class="data-summary">
        <h5>Data Summary Template</h5>
        <pre><code># Data Summary
print("Dataset shape:", df.shape)
print("\\nColumn info:")
print(df.info())
print("\\nStatistical summary:")
print(df.describe())
print("\\nMissing values:")
print(df.isnull().sum())
print("\\nData types:")
print(df.dtypes)</code></pre>
        <button class="copy-btn" onclick="navigator.clipboard.writeText(this.previousElementSibling.textContent)">Copy Code</button>
      </div>
    `);
  }

  getCodeSuggestions() {
    const code = this.getCurrentCellCode();
    const allCode = this.getAllNotebookCode();
    const metadata = this.getNotebookMetadata();

    this.updateResults('<div class="loading">Analyzing context and generating suggestions...</div>');

    // Analyze current context for intelligent suggestions
    const context = this.analyzeCodeContext(code, allCode);

    chrome.runtime.sendMessage({
      action: 'getCodeSuggestions',
      context: code,
      fullContext: allCode,
      metadata: metadata,
      analysis: context
    }, (response) => {
      if (response.success) {
        const suggestions = response.suggestions;
        this.updateResults(`
          <div class="code-suggestions">
            <h5>💡 Intelligent Code Suggestions</h5>
            <div class="context-info">
              <small>📍 Context: ${context.stage} | Language: ${metadata.language} | Type: ${metadata.notebookType}</small>
            </div>
            ${suggestions.map((s, index) => `
              <div class="suggestion-item priority-${s.priority || 'medium'}">
                <div class="suggestion-header">
                  <strong>${s.type.replace(/_/g, ' ').toUpperCase()}:</strong>
                  <span class="suggestion-desc">${s.description}</span>
                  ${s.priority === 'high' ? '<span class="priority-badge">🔥 Recommended</span>' : ''}
                </div>
                <pre><code>${s.code}</code></pre>
                <div class="suggestion-actions">
                  <button class="copy-btn" onclick="navigator.clipboard.writeText(\`${s.code.replace(/`/g, '\\`').replace(/'/g, "\\'")}\`)">📋 Copy</button>
                  ${s.insertable ? `<button class="insert-btn" onclick="window.kaggleAssistant.insertCode(\`${s.code.replace(/`/g, '\\`').replace(/'/g, "\\'")}\`)">➕ Insert</button>` : ''}
                  ${s.explanation ? `<button class="explain-btn" onclick="window.kaggleAssistant.showExplanation(${index})">❓ Explain</button>` : ''}
                </div>
                ${s.explanation ? `<div class="explanation" id="explanation-${index}" style="display: none;">${s.explanation}</div>` : ''}
              </div>
            `).join('')}

            <div class="suggestion-tips">
              <h6>💡 Pro Tips</h6>
              <ul>
                <li>Use <kbd>Ctrl+Shift+A</kbd> to quickly analyze current cell</li>
                <li>Click "Insert" to add code directly to your notebook</li>
                <li>High priority suggestions are most relevant to your current context</li>
              </ul>
            </div>
          </div>
        `);

        // Store suggestions for later use
        this.currentSuggestions = suggestions;
      } else {
        this.updateResults('<div class="error">Error generating suggestions</div>');
      }
    });
  }

  analyzeCodeContext(currentCode, allCode) {
    const context = {
      stage: 'unknown',
      hasData: false,
      hasModel: false,
      hasVisualization: false,
      missingImports: [],
      dataVariables: [],
      modelVariables: []
    };

    // Determine current stage of analysis
    if (allCode.includes('pd.read_csv') || allCode.includes('pd.read_excel')) {
      context.hasData = true;
      context.stage = 'data_loaded';
    }

    if (allCode.includes('train_test_split') || allCode.includes('.fit(')) {
      context.hasModel = true;
      context.stage = 'modeling';
    }

    if (allCode.includes('plt.') || allCode.includes('sns.')) {
      context.hasVisualization = true;
      if (context.stage === 'data_loaded') {
        context.stage = 'exploration';
      }
    }

    if (allCode.includes('.predict(') || allCode.includes('submission')) {
      context.stage = 'prediction';
    }

    // Detect missing common imports
    const commonImports = ['pandas', 'numpy', 'matplotlib', 'seaborn', 'sklearn'];
    commonImports.forEach(lib => {
      if (!allCode.includes(`import ${lib}`) && !allCode.includes(`from ${lib}`)) {
        context.missingImports.push(lib);
      }
    });

    // Extract variable names that look like DataFrames
    const dfMatches = allCode.match(/(\w+)\s*=\s*pd\.read_/g);
    if (dfMatches) {
      context.dataVariables = dfMatches.map(match => match.split('=')[0].trim());
    }

    return context;
  }

  insertCode(code) {
    // Try to insert code into the current cell
    if (this.currentCell) {
      const codeInput = this.currentCell.querySelector('textarea, .CodeMirror, .monaco-editor');
      if (codeInput) {
        if (codeInput.tagName === 'TEXTAREA') {
          codeInput.value += '\n' + code;
          codeInput.dispatchEvent(new Event('input', { bubbles: true }));
        } else {
          // For CodeMirror or Monaco, we'll copy to clipboard and show instruction
          navigator.clipboard.writeText(code);
          this.showNotification('Code copied to clipboard! Paste it in your cell.', 'success');
        }
      }
    } else {
      navigator.clipboard.writeText(code);
      this.showNotification('Code copied to clipboard!', 'info');
    }
  }

  showExplanation(index) {
    const explanation = document.getElementById(`explanation-${index}`);
    if (explanation) {
      explanation.style.display = explanation.style.display === 'none' ? 'block' : 'none';
    }
  }

  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      z-index: 10001;
      animation: slideInRight 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  optimizeCode() {
    this.updateResults(`
      <div class="optimization-tips">
        <h5>Code Optimization Tips</h5>
        <ul>
          <li>Use vectorized operations instead of loops</li>
          <li>Consider using pandas.query() for filtering</li>
          <li>Use appropriate data types (category for strings)</li>
          <li>Cache expensive computations</li>
          <li>Use memory-efficient methods like chunking for large datasets</li>
        </ul>
      </div>
    `);
  }

  addComments() {
    this.updateResults(`
      <div class="comment-templates">
        <h5>Comment Templates</h5>
        <div class="template-item">
          <strong>Data Loading:</strong>
          <code># Load and explore the dataset</code>
        </div>
        <div class="template-item">
          <strong>Data Cleaning:</strong>
          <code># Handle missing values and outliers</code>
        </div>
        <div class="template-item">
          <strong>Feature Engineering:</strong>
          <code># Create new features from existing data</code>
        </div>
        <div class="template-item">
          <strong>Model Training:</strong>
          <code># Train and evaluate the model</code>
        </div>
      </div>
    `);
  }

  addCommonImports() {
    const imports = `# Common Data Science Imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")`;

    this.updateResults(`
      <div class="imports-template">
        <h5>Common Imports</h5>
        <pre><code>${imports}</code></pre>
        <button class="copy-btn" onclick="navigator.clipboard.writeText(\`${imports}\`)">Copy All Imports</button>
      </div>
    `);
  }

  debugError() {
    // Try to detect actual errors in the notebook
    const errors = this.detectNotebookErrors();

    if (errors.length > 0) {
      this.updateResults(`
        <div class="debug-help">
          <h5>🐛 Detected Errors & Solutions</h5>
          ${errors.map(error => `
            <div class="error-item detected">
              <strong>${error.type}:</strong> ${error.message}
              <div class="error-solution">
                <strong>💡 Solution:</strong> ${error.solution}
              </div>
              ${error.code ? `
                <div class="error-fix">
                  <strong>🔧 Fix:</strong>
                  <pre><code>${error.code}</code></pre>
                  <button class="copy-btn" onclick="navigator.clipboard.writeText('${error.code.replace(/'/g, "\\'")}')">Copy Fix</button>
                </div>
              ` : ''}
            </div>
          `).join('')}
        </div>
      `);
    } else {
      this.updateResults(`
        <div class="debug-help">
          <h5>🐛 Common Error Solutions</h5>
          <div class="error-category">
            <h6>Data-related Errors</h6>
            <div class="error-item">
              <strong>KeyError:</strong> Column doesn't exist
              <div class="error-solution">
                Check available columns: <code class="code-highlight">df.columns.tolist()</code>
              </div>
            </div>
            <div class="error-item">
              <strong>ValueError:</strong> Data type mismatch
              <div class="error-solution">
                Check data types: <code class="code-highlight">df.dtypes</code><br>
                Convert types: <code class="code-highlight">df['col'].astype('float')</code>
              </div>
            </div>
            <div class="error-item">
              <strong>IndexError:</strong> Index out of bounds
              <div class="error-solution">
                Check shape: <code class="code-highlight">df.shape</code><br>
                Use safe indexing: <code class="code-highlight">df.iloc[0:5]</code>
              </div>
            </div>
          </div>

          <div class="error-category">
            <h6>Memory & Performance Errors</h6>
            <div class="error-item">
              <strong>MemoryError:</strong> Dataset too large
              <div class="error-solution">
                Use chunking: <code class="code-highlight">pd.read_csv('file.csv', chunksize=1000)</code><br>
                Optimize dtypes: <code class="code-highlight">df = df.astype({'col': 'category'})</code>
              </div>
            </div>
            <div class="error-item">
              <strong>PerformanceWarning:</strong> Slow operations
              <div class="error-solution">
                Use vectorized operations instead of loops<br>
                Avoid chained assignments: <code class="code-highlight">df.loc[mask, 'col'] = value</code>
              </div>
            </div>
          </div>

          <div class="error-category">
            <h6>Machine Learning Errors</h6>
            <div class="error-item">
              <strong>NotFittedError:</strong> Model not trained
              <div class="error-solution">
                Fit model first: <code class="code-highlight">model.fit(X_train, y_train)</code>
              </div>
            </div>
            <div class="error-item">
              <strong>ValueError: Input contains NaN:</strong> Missing values
              <div class="error-solution">
                Handle missing values: <code class="code-highlight">X = X.fillna(X.mean())</code>
              </div>
            </div>
          </div>

          <div class="debug-tools">
            <h6>🔍 Debugging Tools</h6>
            <pre><code># Debug information template
print("DataFrame Info:")
print(f"Shape: {df.shape}")
print(f"Columns: {df.columns.tolist()}")
print(f"Data types: {df.dtypes.to_dict()}")
print(f"Missing values: {df.isnull().sum().to_dict()}")
print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Check for common issues
print("\\nCommon Issues Check:")
print(f"Duplicate rows: {df.duplicated().sum()}")
print(f"Infinite values: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
print(f"Very large values: {(df.select_dtypes(include=[np.number]) > 1e10).sum().sum()}")</code></pre>
            <button class="copy-btn" onclick="navigator.clipboard.writeText(this.previousElementSibling.textContent)">Copy Debug Code</button>
          </div>
        </div>
      `);
    }
  }

  detectNotebookErrors() {
    const errors = [];

    // Look for error outputs in the notebook
    const errorOutputs = document.querySelectorAll('.output-error, .error-output, .stderr');

    errorOutputs.forEach(errorElement => {
      const errorText = errorElement.textContent;

      if (errorText.includes('KeyError')) {
        const match = errorText.match(/KeyError: ['"]([^'"]+)['"]/);
        const columnName = match ? match[1] : 'unknown';
        errors.push({
          type: 'KeyError',
          message: `Column '${columnName}' not found`,
          solution: 'Check if the column exists in your DataFrame',
          code: `# Check available columns\nprint("Available columns:", df.columns.tolist())\n\n# Alternative: use get() method\nvalue = df.get('${columnName}', 'default_value')`
        });
      }

      if (errorText.includes('ValueError') && errorText.includes('could not convert')) {
        errors.push({
          type: 'ValueError',
          message: 'Data type conversion failed',
          solution: 'Clean your data before conversion',
          code: `# Clean and convert data\ndf['column'] = pd.to_numeric(df['column'], errors='coerce')\n# or\ndf['column'] = df['column'].astype(str).str.replace('[^0-9.]', '', regex=True)\ndf['column'] = pd.to_numeric(df['column'])`
        });
      }

      if (errorText.includes('MemoryError')) {
        errors.push({
          type: 'MemoryError',
          message: 'Not enough memory to complete operation',
          solution: 'Use chunking or optimize data types',
          code: `# Read data in chunks\nchunk_list = []\nfor chunk in pd.read_csv('large_file.csv', chunksize=1000):\n    # Process chunk\n    processed_chunk = chunk.dropna()\n    chunk_list.append(processed_chunk)\n\ndf = pd.concat(chunk_list, ignore_index=True)`
        });
      }

      if (errorText.includes('ModuleNotFoundError')) {
        const match = errorText.match(/No module named ['"]([^'"]+)['"]/);
        const moduleName = match ? match[1] : 'unknown';
        errors.push({
          type: 'ModuleNotFoundError',
          message: `Module '${moduleName}' not installed`,
          solution: 'Install the required module',
          code: `# Install missing module\n!pip install ${moduleName}\n\n# Then import\nimport ${moduleName}`
        });
      }
    });

    return errors;
  }

  showPerformanceTips() {
    this.updateResults(`
      <div class="performance-tips">
        <h5>Performance Optimization</h5>
        <ul>
          <li>Use .loc and .iloc for indexing</li>
          <li>Avoid chained assignments</li>
          <li>Use .copy() when modifying DataFrames</li>
          <li>Consider using Dask for large datasets</li>
          <li>Profile your code with %%time or %%timeit</li>
          <li>Use categorical data types for strings</li>
        </ul>
      </div>
    `);
  }

  // New enhanced methods
  analyzeAllCells() {
    this.updateResults('<div class="loading">Analyzing all cells...</div>');

    const allCode = this.getAllNotebookCode();
    const metadata = this.getNotebookMetadata();

    chrome.runtime.sendMessage({
      action: 'analyzeCode',
      code: allCode,
      metadata: metadata
    }, (response) => {
      if (response.success) {
        const analysis = response.analysis;
        this.updateResults(`
          <div class="full-analysis">
            <h5>Complete Notebook Analysis</h5>
            <div class="analysis-section">
              <h6>📊 Overview</h6>
              <p><strong>Total Lines:</strong> ${analysis.complexity.lines}</p>
              <p><strong>Functions:</strong> ${analysis.complexity.functions}</p>
              <p><strong>Classes:</strong> ${analysis.complexity.classes}</p>
              <p><strong>Cells:</strong> ${metadata.cellCount}</p>
            </div>
            <div class="analysis-section">
              <h6>🔍 Detected Patterns</h6>
              ${analysis.patterns.map(p => `<div class="pattern-item">✓ ${p}</div>`).join('')}
            </div>
            <div class="analysis-section">
              <h6>💡 Recommendations</h6>
              ${analysis.suggestions.map(s => `<div class="suggestion-item">• ${s}</div>`).join('')}
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 85%"></div>
            </div>
            <small>Analysis Score: 85/100</small>
          </div>
        `);
      } else {
        this.updateResults('<div class="error">Error analyzing notebook</div>');
      }
    });
  }

  formatCode() {
    const code = this.getCurrentCellCode();
    if (!code.trim()) {
      this.updateResults('<div class="warning">No code found in current cell</div>');
      return;
    }

    this.updateResults(`
      <div class="code-formatting">
        <h5>Code Formatting Suggestions</h5>
        <div class="format-section">
          <h6>🎨 Style Improvements</h6>
          <ul>
            <li>Use consistent indentation (4 spaces)</li>
            <li>Add blank lines between logical sections</li>
            <li>Use descriptive variable names</li>
            <li>Follow PEP 8 naming conventions</li>
          </ul>
        </div>
        <div class="format-section">
          <h6>📝 Formatted Code Template</h6>
          <pre><code># Improved code structure
import pandas as pd
import numpy as np

# Load and prepare data
def load_data(file_path):
    """Load and return cleaned dataset."""
    df = pd.read_csv(file_path)
    return df.dropna()

# Main analysis
data = load_data('/kaggle/input/data.csv')
print(f"Dataset shape: {data.shape}")

# Visualization
import matplotlib.pyplot as plt
plt.figure(figsize=(10, 6))
data.hist(bins=30)
plt.tight_layout()
plt.show()</code></pre>
          <button class="copy-btn" onclick="navigator.clipboard.writeText(this.previousElementSibling.textContent)">Copy Formatted Code</button>
        </div>
      </div>
    `);
  }

  exportTemplates() {
    this.updateResults(`
      <div class="export-templates">
        <h5>📤 Export Code Templates</h5>
        <div class="template-category">
          <h6>Data Science Workflow</h6>
          <button class="action-btn" onclick="navigator.clipboard.writeText(\`# Complete Data Science Workflow
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix

# 1. Data Loading
df = pd.read_csv('/kaggle/input/data.csv')
print(f"Dataset shape: {df.shape}")

# 2. Exploratory Data Analysis
df.info()
df.describe()
df.isnull().sum()

# 3. Data Preprocessing
# Handle missing values
df = df.dropna()

# Feature engineering
# Add your feature engineering here

# 4. Model Training
X = df.drop('target', axis=1)
y = df['target']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Scale features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Train model
model = RandomForestClassifier(random_state=42)
model.fit(X_train_scaled, y_train)

# 5. Evaluation
predictions = model.predict(X_test_scaled)
print(classification_report(y_test, predictions))
\`)">Copy Complete Workflow</button>
        </div>

        <div class="template-category">
          <h6>Visualization Templates</h6>
          <button class="action-btn" onclick="navigator.clipboard.writeText(\`# Comprehensive Visualization Suite
import matplotlib.pyplot as plt
import seaborn as sns
plt.style.use('seaborn-v0_8')

# Set up the plotting area
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# Distribution plot
axes[0, 0].hist(df['column'], bins=30, alpha=0.7)
axes[0, 0].set_title('Distribution')

# Correlation heatmap
sns.heatmap(df.corr(), annot=True, ax=axes[0, 1])
axes[0, 1].set_title('Correlation Matrix')

# Box plot
df.boxplot(ax=axes[1, 0])
axes[1, 0].set_title('Box Plot')

# Scatter plot
axes[1, 1].scatter(df['x'], df['y'])
axes[1, 1].set_title('Scatter Plot')

plt.tight_layout()
plt.show()
\`)">Copy Visualization Suite</button>
        </div>

        <div class="template-category">
          <h6>Competition Template</h6>
          <button class="action-btn" onclick="navigator.clipboard.writeText(\`# Kaggle Competition Template
import pandas as pd
import numpy as np
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score

# Load competition data
train_df = pd.read_csv('/kaggle/input/train.csv')
test_df = pd.read_csv('/kaggle/input/test.csv')

# Feature engineering
def feature_engineering(df):
    # Add your feature engineering here
    return df

train_df = feature_engineering(train_df)
test_df = feature_engineering(test_df)

# Prepare data
X = train_df.drop(['target'], axis=1)
y = train_df['target']

# Cross-validation
cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
model = RandomForestClassifier(random_state=42)
cv_scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
print(f"CV Score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

# Train final model
model.fit(X, y)

# Make predictions
predictions = model.predict(test_df)

# Create submission
submission = pd.DataFrame({
    'id': test_df['id'],
    'target': predictions
})
submission.to_csv('submission.csv', index=False)
print(f"Submission shape: {submission.shape}")
\`)">Copy Competition Template</button>
        </div>
      </div>
    `);
  }

  setupTypeSpecificListeners(type) {
    // Add event listeners for type-specific buttons
    switch (type) {
      case 'competition':
        document.getElementById('competition-analysis')?.addEventListener('click', () => this.analyzeCompetitionData());
        document.getElementById('submission-tips')?.addEventListener('click', () => this.showSubmissionTips());
        document.getElementById('leaderboard-strategy')?.addEventListener('click', () => this.showLeaderboardStrategy());
        break;
      case 'dataset':
        document.getElementById('dataset-exploration')?.addEventListener('click', () => this.exploreDataset());
        document.getElementById('data-quality')?.addEventListener('click', () => this.checkDataQuality());
        document.getElementById('feature-analysis')?.addEventListener('click', () => this.analyzeFeatures());
        break;
      default:
        document.getElementById('notebook-structure')?.addEventListener('click', () => this.improveNotebookStructure());
        document.getElementById('documentation')?.addEventListener('click', () => this.addDocumentation());
    }
  }

  // Competition-specific methods
  analyzeCompetitionData() {
    this.updateResults(`
      <div class="competition-analysis">
        <h5>Competition Data Analysis</h5>
        <pre><code># Competition Data Analysis Template
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Load competition data
train_df = pd.read_csv('/kaggle/input/train.csv')
test_df = pd.read_csv('/kaggle/input/test.csv')

# Basic exploration
print("Train shape:", train_df.shape)
print("Test shape:", test_df.shape)
print("\\nTrain columns:", train_df.columns.tolist())
print("\\nMissing values:")
print(train_df.isnull().sum())

# Target analysis (if applicable)
if 'target' in train_df.columns:
    print("\\nTarget distribution:")
    print(train_df['target'].value_counts())

# Quick visualization
plt.figure(figsize=(12, 8))
train_df.hist(bins=30, figsize=(12, 8))
plt.tight_layout()
plt.show()</code></pre>
        <button class="copy-btn" onclick="navigator.clipboard.writeText(this.previousElementSibling.textContent)">Copy Code</button>
      </div>
    `);
  }

  showSubmissionTips() {
    this.updateResults(`
      <div class="submission-tips">
        <h5>Submission Best Practices</h5>
        <ul>
          <li><strong>Validation Strategy:</strong> Use cross-validation that matches the competition metric</li>
          <li><strong>Feature Engineering:</strong> Create meaningful features from domain knowledge</li>
          <li><strong>Model Ensemble:</strong> Combine multiple models for better performance</li>
          <li><strong>Submission Format:</strong> Always check the sample submission format</li>
          <li><strong>Leakage Check:</strong> Ensure no future information in features</li>
          <li><strong>Local Validation:</strong> Trust your local CV score</li>
        </ul>
        <div class="code-template">
          <h6>Submission Template:</h6>
          <pre><code># Create submission file
submission = pd.DataFrame({
    'id': test_df['id'],
    'target': predictions
})
submission.to_csv('submission.csv', index=False)
print("Submission shape:", submission.shape)</code></pre>
        </div>
      </div>
    `);
  }

  showLeaderboardStrategy() {
    this.updateResults(`
      <div class="leaderboard-strategy">
        <h5>Leaderboard Strategy</h5>
        <div class="strategy-item">
          <strong>Public vs Private LB:</strong>
          <p>Focus on robust validation rather than public LB score</p>
        </div>
        <div class="strategy-item">
          <strong>Overfitting Prevention:</strong>
          <p>Use techniques like regularization and early stopping</p>
        </div>
        <div class="strategy-item">
          <strong>Feature Selection:</strong>
          <p>Remove features that don't generalize well</p>
        </div>
        <div class="strategy-item">
          <strong>Model Diversity:</strong>
          <p>Ensemble different types of models (tree-based, neural networks, linear)</p>
        </div>
      </div>
    `);
  }

  // Dataset-specific methods
  exploreDataset() {
    this.updateResults(`
      <div class="dataset-exploration">
        <h5>Dataset Exploration Template</h5>
        <pre><code># Comprehensive Dataset Exploration
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Load dataset
df = pd.read_csv('/kaggle/input/your-dataset/file.csv')

# Basic information
print("Dataset shape:", df.shape)
print("\\nColumn types:")
print(df.dtypes)
print("\\nMemory usage:")
print(df.memory_usage(deep=True))

# Statistical summary
print("\\nStatistical Summary:")
print(df.describe(include='all'))

# Missing values analysis
missing_data = df.isnull().sum()
missing_percent = 100 * missing_data / len(df)
missing_table = pd.DataFrame({
    'Missing Count': missing_data,
    'Missing Percentage': missing_percent
})
print("\\nMissing Data:")
print(missing_table[missing_table['Missing Count'] > 0])

# Correlation analysis for numeric columns
numeric_cols = df.select_dtypes(include=[np.number]).columns
if len(numeric_cols) > 1:
    plt.figure(figsize=(10, 8))
    sns.heatmap(df[numeric_cols].corr(), annot=True, cmap='coolwarm')
    plt.title('Correlation Matrix')
    plt.show()</code></pre>
        <button class="copy-btn" onclick="navigator.clipboard.writeText(this.previousElementSibling.textContent)">Copy Code</button>
      </div>
    `);
  }

  checkDataQuality() {
    this.updateResults(`
      <div class="data-quality">
        <h5>Data Quality Assessment</h5>
        <pre><code># Data Quality Checks
def data_quality_report(df):
    report = {}

    # Duplicates
    duplicates = df.duplicated().sum()
    report['duplicates'] = duplicates

    # Missing values
    missing = df.isnull().sum().sum()
    report['missing_values'] = missing

    # Data types consistency
    report['data_types'] = df.dtypes.value_counts().to_dict()

    # Outliers (for numeric columns)
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    outliers = {}
    for col in numeric_cols:
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        outliers[col] = ((df[col] < (Q1 - 1.5 * IQR)) |
                        (df[col] > (Q3 + 1.5 * IQR))).sum()
    report['outliers'] = outliers

    return report

# Run quality check
quality_report = data_quality_report(df)
print("Data Quality Report:")
for key, value in quality_report.items():
    print(f"{key}: {value}")</code></pre>
        <button class="copy-btn" onclick="navigator.clipboard.writeText(this.previousElementSibling.textContent)">Copy Code</button>
      </div>
    `);
  }

  analyzeFeatures() {
    this.updateResults(`
      <div class="feature-analysis">
        <h5>Feature Analysis Tools</h5>
        <pre><code># Feature Analysis and Engineering
import pandas as pd
import numpy as np
from sklearn.feature_selection import mutual_info_regression, mutual_info_classif

def analyze_features(df, target_col=None):
    analysis = {}

    # Feature types
    numeric_features = df.select_dtypes(include=[np.number]).columns.tolist()
    categorical_features = df.select_dtypes(include=['object']).columns.tolist()

    analysis['numeric_features'] = len(numeric_features)
    analysis['categorical_features'] = len(categorical_features)

    # Feature importance (if target provided)
    if target_col and target_col in df.columns:
        X = df.drop(columns=[target_col])
        y = df[target_col]

        # Prepare features for mutual information
        X_encoded = pd.get_dummies(X, drop_first=True)

        # Calculate mutual information
        if y.dtype == 'object' or len(y.unique()) < 10:
            mi_scores = mutual_info_classif(X_encoded, y)
        else:
            mi_scores = mutual_info_regression(X_encoded, y)

        feature_importance = pd.DataFrame({
            'feature': X_encoded.columns,
            'importance': mi_scores
        }).sort_values('importance', ascending=False)

        print("Top 10 Most Important Features:")
        print(feature_importance.head(10))

    return analysis

# Run feature analysis
feature_analysis = analyze_features(df, target_col='your_target_column')
print("Feature Analysis Results:")
print(feature_analysis)</code></pre>
        <button class="copy-btn" onclick="navigator.clipboard.writeText(this.previousElementSibling.textContent)">Copy Code</button>
      </div>
    `);
  }

  // General notebook methods
  improveNotebookStructure() {
    this.updateResults(`
      <div class="notebook-structure">
        <h5>Notebook Structure Best Practices</h5>
        <div class="structure-section">
          <h6>Recommended Structure:</h6>
          <ol>
            <li><strong>Introduction & Objective</strong> - Clear problem statement</li>
            <li><strong>Data Loading & Overview</strong> - Import and basic exploration</li>
            <li><strong>Exploratory Data Analysis</strong> - Detailed data analysis</li>
            <li><strong>Data Preprocessing</strong> - Cleaning and feature engineering</li>
            <li><strong>Model Development</strong> - Training and validation</li>
            <li><strong>Results & Evaluation</strong> - Performance metrics</li>
            <li><strong>Conclusions</strong> - Key insights and next steps</li>
          </ol>
        </div>
        <div class="markdown-templates">
          <h6>Markdown Templates:</h6>
          <pre><code># 📊 Project Title
## Objective
Brief description of what we're trying to achieve.

## 📁 Data Overview
- Dataset size: X rows, Y columns
- Target variable: [description]
- Key features: [list main features]

## 🔍 Key Findings
- Finding 1
- Finding 2
- Finding 3</code></pre>
        </div>
      </div>
    `);
  }

  addDocumentation() {
    this.updateResults(`
      <div class="documentation-templates">
        <h5>Documentation Templates</h5>
        <div class="doc-section">
          <strong>Function Documentation:</strong>
          <pre><code>def analyze_data(df, target_col):
    """
    Analyze dataset and provide insights.

    Parameters:
    -----------
    df : pandas.DataFrame
        Input dataset
    target_col : str
        Name of target column

    Returns:
    --------
    dict
        Analysis results including statistics and insights
    """
    # Function implementation here
    pass</code></pre>
        </div>
        <div class="doc-section">
          <strong>Cell Documentation:</strong>
          <pre><code># =============================================================================
# DATA PREPROCESSING
# =============================================================================
# This section handles:
# 1. Missing value imputation
# 2. Feature scaling
# 3. Categorical encoding</code></pre>
        </div>
      </div>
    `);
  }

  observeNotebookChanges() {
    // Enhanced notebook change observation
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check for new cells
          const addedNodes = Array.from(mutation.addedNodes);
          const newCells = addedNodes.filter(node =>
            node.nodeType === 1 &&
            (node.classList?.contains('cell') ||
             node.classList?.contains('code-cell') ||
             node.getAttribute?.('data-testid') === 'code-cell')
          );

          if (newCells.length > 0) {
            this.updateContext();
          }

          // Check for output changes
          const outputNodes = addedNodes.filter(node =>
            node.nodeType === 1 &&
            (node.classList?.contains('output') ||
             node.classList?.contains('cell-output'))
          );

          if (outputNodes.length > 0) {
            this.handleNewOutput(outputNodes);
          }
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.notebookObserver = observer;
  }

  updateContext() {
    // Update the assistant's context based on notebook changes
    const metadata = this.getNotebookMetadata();
    const allCode = this.getAllNotebookCode();

    // Detect patterns in the code
    this.detectCodePatterns(allCode);

    // Update suggestions based on current state
    this.updateContextualSuggestions(metadata, allCode);
  }

  detectCodePatterns(code) {
    const patterns = {
      dataLoading: /pd\.read_csv|pd\.read_excel|pd\.read_json/g.test(code),
      visualization: /plt\.|sns\.|plotly/g.test(code),
      machineLearning: /sklearn|tensorflow|torch|xgboost|lightgbm/g.test(code),
      dataProcessing: /fillna|dropna|groupby|merge|join/g.test(code),
      featureEngineering: /LabelEncoder|OneHotEncoder|StandardScaler|MinMaxScaler/g.test(code)
    };

    this.currentPatterns = patterns;
    return patterns;
  }

  updateContextualSuggestions(metadata, code) {
    // This could be enhanced to provide more intelligent suggestions
    // based on the current state of the notebook
    console.log('Context updated:', { metadata, patterns: this.currentPatterns });
  }

  handleNewOutput(outputNodes) {
    // Handle new cell outputs - could provide suggestions based on results
    outputNodes.forEach(node => {
      const text = node.textContent;
      if (text.includes('Error') || text.includes('Exception')) {
        this.suggestErrorFix(text);
      }
    });
  }

  suggestErrorFix(errorText) {
    // Provide quick error fix suggestions
    console.log('Error detected:', errorText);
    // Could show a notification or update the sidebar with error-specific help
  }
}

// Enhanced initialization with better integration
class KaggleNotebookIntegration {
  constructor() {
    this.assistant = null;
    this.cellObserver = null;
    this.outputObserver = null;
    this.init();
  }

  init() {
    // Wait for Kaggle notebook to be fully loaded
    this.waitForNotebookReady().then(() => {
      this.assistant = new KaggleAssistant();
      this.setupAdvancedIntegration();
      window.kaggleAssistant = this.assistant; // Make globally accessible
    });
  }

  async waitForNotebookReady() {
    return new Promise((resolve) => {
      const checkReady = () => {
        const notebook = document.querySelector('.notebook-container, [data-testid="notebook-container"]');
        const cells = document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]');

        if (notebook && cells.length > 0) {
          resolve();
        } else {
          setTimeout(checkReady, 500);
        }
      };
      checkReady();
    });
  }

  setupAdvancedIntegration() {
    // Enhanced cell monitoring
    this.setupCellObserver();

    // Output monitoring for real-time feedback
    this.setupOutputObserver();

    // Keyboard shortcuts for power users
    this.setupAdvancedShortcuts();

    // Context menu integration
    this.setupContextMenu();

    // Auto-suggestions based on cell content
    this.setupAutoSuggestions();
  }

  setupCellObserver() {
    this.cellObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // New cells added
          const addedCells = Array.from(mutation.addedNodes).filter(node =>
            node.nodeType === 1 &&
            (node.classList?.contains('cell') ||
             node.classList?.contains('code-cell') ||
             node.getAttribute?.('data-testid') === 'code-cell')
          );

          if (addedCells.length > 0) {
            this.onCellsAdded(addedCells);
          }

          // Cells removed
          const removedCells = Array.from(mutation.removedNodes).filter(node =>
            node.nodeType === 1 &&
            (node.classList?.contains('cell') ||
             node.classList?.contains('code-cell') ||
             node.getAttribute?.('data-testid') === 'code-cell')
          );

          if (removedCells.length > 0) {
            this.onCellsRemoved(removedCells);
          }
        }

        // Cell content changes
        if (mutation.type === 'characterData' ||
            (mutation.type === 'childList' && mutation.target.closest('.cell, .code-cell'))) {
          this.onCellContentChanged(mutation.target);
        }
      });
    });

    this.cellObserver.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true
    });
  }

  setupOutputObserver() {
    this.outputObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          const addedOutputs = Array.from(mutation.addedNodes).filter(node =>
            node.nodeType === 1 &&
            (node.classList?.contains('output') ||
             node.classList?.contains('cell-output') ||
             node.classList?.contains('output-area'))
          );

          if (addedOutputs.length > 0) {
            this.onOutputAdded(addedOutputs);
          }
        }
      });
    });

    this.outputObserver.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  setupAdvancedShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+H for help
      if (e.ctrlKey && e.shiftKey && e.key === 'H') {
        e.preventDefault();
        this.showQuickHelp();
      }

      // Ctrl+Shift+S for smart suggestions
      if (e.ctrlKey && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        this.assistant.getCodeSuggestions();
      }

      // Ctrl+Shift+D for debug current cell
      if (e.ctrlKey && e.shiftKey && e.key === 'D') {
        e.preventDefault();
        this.assistant.debugError();
      }

      // Ctrl+Shift+O for optimize current cell
      if (e.ctrlKey && e.shiftKey && e.key === 'O') {
        e.preventDefault();
        this.assistant.optimizeCode();
      }
    });
  }

  setupContextMenu() {
    // Add context menu to cells for quick actions
    document.addEventListener('contextmenu', (e) => {
      const cell = e.target.closest('.cell, .code-cell, [data-testid="code-cell"]');
      if (cell) {
        e.preventDefault();
        this.showCellContextMenu(e, cell);
      }
    });
  }

  setupAutoSuggestions() {
    let suggestionTimeout;

    document.addEventListener('input', (e) => {
      const cell = e.target.closest('.cell, .code-cell, [data-testid="code-cell"]');
      if (cell) {
        clearTimeout(suggestionTimeout);
        suggestionTimeout = setTimeout(() => {
          this.checkForAutoSuggestions(cell);
        }, 2000); // Wait 2 seconds after user stops typing
      }
    });
  }

  onCellsAdded(cells) {
    console.log('New cells added:', cells.length);
    this.assistant.updateCellCount();

    // Add assistant indicators to new cells
    cells.forEach(cell => {
      this.addCellEnhancements(cell);
    });
  }

  onCellsRemoved(cells) {
    console.log('Cells removed:', cells.length);
    this.assistant.updateCellCount();
  }

  onCellContentChanged(target) {
    const cell = target.closest('.cell, .code-cell, [data-testid="code-cell"]');
    if (cell) {
      // Update context when cell content changes
      this.assistant.updateContextInfo();
    }
  }

  onOutputAdded(outputs) {
    outputs.forEach(output => {
      const text = output.textContent;

      // Check for errors
      if (text.includes('Error') || text.includes('Exception') || text.includes('Traceback')) {
        this.showErrorNotification(output);
      }

      // Check for warnings
      if (text.includes('Warning') || text.includes('DeprecationWarning')) {
        this.showWarningNotification(output);
      }

      // Check for successful completions
      if (text.includes('Accuracy:') || text.includes('Score:') || text.includes('completed')) {
        this.showSuccessNotification(output);
      }
    });
  }

  addCellEnhancements(cell) {
    // Add a small assistant indicator
    const indicator = document.createElement('div');
    indicator.className = 'assistant-indicator';
    indicator.innerHTML = '🤖';
    indicator.title = 'Right-click for AI assistance';
    indicator.style.cssText = `
      position: absolute;
      top: 5px;
      right: 5px;
      width: 20px;
      height: 20px;
      background: rgba(76, 175, 80, 0.8);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      cursor: pointer;
      z-index: 1000;
      opacity: 0.7;
      transition: opacity 0.2s;
    `;

    indicator.addEventListener('click', (e) => {
      e.stopPropagation();
      this.assistant.currentCell = cell;
      this.assistant.getCodeSuggestions();
    });

    cell.style.position = 'relative';
    cell.appendChild(indicator);
  }

  showCellContextMenu(e, cell) {
    const menu = document.createElement('div');
    menu.className = 'cell-context-menu';
    menu.innerHTML = `
      <div class="context-menu-item" data-action="analyze">🔍 Analyze Cell</div>
      <div class="context-menu-item" data-action="suggest">💡 Get Suggestions</div>
      <div class="context-menu-item" data-action="optimize">⚡ Optimize Code</div>
      <div class="context-menu-item" data-action="debug">🐛 Debug Errors</div>
      <div class="context-menu-item" data-action="format">🎨 Format Code</div>
    `;

    menu.style.cssText = `
      position: fixed;
      top: ${e.clientY}px;
      left: ${e.clientX}px;
      background: #333;
      border: 1px solid #555;
      border-radius: 6px;
      padding: 8px 0;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;

    menu.addEventListener('click', (e) => {
      const action = e.target.dataset.action;
      if (action) {
        this.assistant.currentCell = cell;
        this.handleContextMenuAction(action);
      }
      menu.remove();
    });

    document.addEventListener('click', () => menu.remove(), { once: true });
    document.body.appendChild(menu);
  }

  handleContextMenuAction(action) {
    switch (action) {
      case 'analyze':
        this.assistant.analyzeData();
        break;
      case 'suggest':
        this.assistant.getCodeSuggestions();
        break;
      case 'optimize':
        this.assistant.optimizeCode();
        break;
      case 'debug':
        this.assistant.debugError();
        break;
      case 'format':
        this.assistant.formatCode();
        break;
    }
  }

  checkForAutoSuggestions(cell) {
    const code = this.assistant.extractCodeFromCell(cell);

    // Auto-suggest based on common patterns
    if (code.includes('pd.read_csv') && !code.includes('.info()')) {
      this.showAutoSuggestion('Add data exploration', 'df.info()\ndf.describe()\ndf.head()');
    }

    if (code.includes('train_test_split') && !code.includes('random_state')) {
      this.showAutoSuggestion('Add random_state for reproducibility', 'random_state=42');
    }

    if (code.includes('plt.') && !code.includes('plt.show()')) {
      this.showAutoSuggestion('Add plt.show() to display plot', 'plt.show()');
    }
  }

  showAutoSuggestion(title, code) {
    const suggestion = document.createElement('div');
    suggestion.className = 'auto-suggestion';
    suggestion.innerHTML = `
      <div class="suggestion-title">💡 ${title}</div>
      <div class="suggestion-code">${code}</div>
      <button onclick="navigator.clipboard.writeText('${code}'); this.parentElement.remove();">Copy</button>
      <button onclick="this.parentElement.remove();">Dismiss</button>
    `;

    suggestion.style.cssText = `
      position: fixed;
      top: 100px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px;
      border-radius: 6px;
      z-index: 10001;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;

    document.body.appendChild(suggestion);

    setTimeout(() => suggestion.remove(), 10000); // Auto-remove after 10 seconds
  }

  showErrorNotification(output) {
    this.assistant.showNotification('Error detected! Click debug button for help.', 'error');
  }

  showWarningNotification(output) {
    this.assistant.showNotification('Warning detected. Check your code.', 'warning');
  }

  showSuccessNotification(output) {
    this.assistant.showNotification('Great! Your code executed successfully.', 'success');
  }

  showQuickHelp() {
    this.assistant.updateResults(`
      <div class="quick-help">
        <h5>⚡ Quick Help</h5>
        <div class="help-section">
          <h6>Keyboard Shortcuts</h6>
          <ul>
            <li><kbd>Ctrl+Shift+K</kbd> - Toggle sidebar</li>
            <li><kbd>Ctrl+Shift+A</kbd> - Analyze current cell</li>
            <li><kbd>Ctrl+Shift+S</kbd> - Smart suggestions</li>
            <li><kbd>Ctrl+Shift+D</kbd> - Debug current cell</li>
            <li><kbd>Ctrl+Shift+O</kbd> - Optimize code</li>
            <li><kbd>Ctrl+Shift+H</kbd> - Show this help</li>
          </ul>
        </div>
        <div class="help-section">
          <h6>Quick Actions</h6>
          <ul>
            <li>Right-click any cell for context menu</li>
            <li>Click 🤖 icon on cells for suggestions</li>
            <li>Auto-suggestions appear as you type</li>
          </ul>
        </div>
      </div>
    `);
  }
}

// Initialize the enhanced integration
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new KaggleNotebookIntegration();
  });
} else {
  new KaggleNotebookIntegration();
}

})(); // End of IIFE to prevent global scope pollution
