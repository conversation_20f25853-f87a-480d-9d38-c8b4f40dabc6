// Debug version - minimal content script
(function() {
    'use strict';
    
    console.log('🔍 DEBUG: Script starting...');
    console.log('🔍 DEBUG: URL:', window.location.href);
    console.log('🔍 DEBUG: Domain:', window.location.hostname);
    console.log('🔍 DEBUG: Path:', window.location.pathname);
    
    // Check if already loaded
    if (window.debugKaggleLoaded) {
        console.log('🔍 DEBUG: Already loaded, exiting');
        return;
    }
    window.debugKaggleLoaded = true;
    
    // Check page type
    const isKaggle = window.location.hostname.includes('kaggle.com');
    const isNotebook = window.location.pathname.includes('/code/') || window.location.pathname.includes('/notebook/');
    
    console.log('🔍 DEBUG: Is Kaggle?', isKaggle);
    console.log('🔍 DEBUG: Is Notebook?', isNotebook);
    
    if (!isKaggle) {
        console.log('❌ DEBUG: Not on Kaggle, stopping');
        return;
    }
    
    if (!isNotebook) {
        console.log('❌ DEBUG: Not on notebook page, stopping');
        return;
    }
    
    console.log('✅ DEBUG: On Kaggle notebook, proceeding...');
    
    // Wait for page to load
    function waitForPage() {
        console.log('🔍 DEBUG: Waiting for page elements...');
        
        const checkElements = () => {
            const body = document.body;
            const notebook = document.querySelector('.notebook-container, [data-testid="notebook-container"], .notebook, #notebook');
            const cells = document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]');
            
            console.log('🔍 DEBUG: Body exists?', !!body);
            console.log('🔍 DEBUG: Notebook container?', !!notebook);
            console.log('🔍 DEBUG: Cells found:', cells.length);
            
            if (body) {
                console.log('✅ DEBUG: Page ready, creating sidebar...');
                createDebugSidebar();
            } else {
                console.log('⏳ DEBUG: Page not ready, retrying...');
                setTimeout(checkElements, 500);
            }
        };
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', checkElements);
        } else {
            checkElements();
        }
    }
    
    function createDebugSidebar() {
        console.log('🚀 DEBUG: Creating sidebar...');
        
        // Remove any existing debug sidebar
        const existing = document.getElementById('debug-sidebar');
        if (existing) {
            console.log('🔄 DEBUG: Removing existing sidebar');
            existing.remove();
        }
        
        // Create sidebar
        const sidebar = document.createElement('div');
        sidebar.id = 'debug-sidebar';
        sidebar.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            width: 350px !important;
            height: 100vh !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            z-index: 999999 !important;
            padding: 20px !important;
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5) !important;
            font-family: Arial, sans-serif !important;
            overflow-y: auto !important;
            border: 2px solid #fff !important;
        `;
        
        const debugInfo = {
            url: window.location.href,
            domain: window.location.hostname,
            path: window.location.pathname,
            readyState: document.readyState,
            bodyExists: !!document.body,
            notebookContainer: !!document.querySelector('.notebook-container, [data-testid="notebook-container"], .notebook, #notebook'),
            cellsCount: document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]').length,
            timestamp: new Date().toLocaleTimeString()
        };
        
        sidebar.innerHTML = `
            <div style="margin-bottom: 20px; border-bottom: 1px solid rgba(255,255,255,0.3); padding-bottom: 15px;">
                <h2 style="margin: 0; font-size: 18px; color: #fff;">🔍 DEBUG SIDEBAR</h2>
                <p style="margin: 5px 0; font-size: 12px; opacity: 0.8;">Extension is working!</p>
            </div>
            
            <div style="margin-bottom: 15px;">
                <button onclick="this.parentElement.parentElement.remove()" 
                        style="background: #f44336; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                    ❌ Close
                </button>
                <button onclick="window.location.reload()" 
                        style="background: #2196F3; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    🔄 Reload
                </button>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h3 style="margin: 0 0 10px 0; font-size: 14px;">📍 Page Info:</h3>
                <div style="font-size: 11px; line-height: 1.4;">
                    <div><strong>URL:</strong> ${debugInfo.url}</div>
                    <div><strong>Domain:</strong> ${debugInfo.domain}</div>
                    <div><strong>Path:</strong> ${debugInfo.path}</div>
                    <div><strong>Ready State:</strong> ${debugInfo.readyState}</div>
                    <div><strong>Time:</strong> ${debugInfo.timestamp}</div>
                </div>
            </div>
            
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <h3 style="margin: 0 0 10px 0; font-size: 14px;">🔍 Elements Found:</h3>
                <div style="font-size: 11px; line-height: 1.4;">
                    <div><strong>Body:</strong> ${debugInfo.bodyExists ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Notebook Container:</strong> ${debugInfo.notebookContainer ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Code Cells:</strong> ${debugInfo.cellsCount}</div>
                </div>
            </div>
            
            <div style="background: rgba(76, 175, 80, 0.2); padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #4CAF50;">
                <h3 style="margin: 0 0 10px 0; font-size: 14px; color: #4CAF50;">✅ Success!</h3>
                <p style="margin: 0; font-size: 12px;">The extension is working correctly. The sidebar appears and can detect the page.</p>
            </div>
            
            <div style="margin-top: 20px;">
                <button onclick="testConnection()"
                        style="background: #FF9800; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%; margin-bottom: 8px; font-size: 12px;">
                    🔍 Test Background Script
                </button>
                <button onclick="loadFullExtension()"
                        style="background: #4CAF50; border: none; color: white; padding: 12px 16px; border-radius: 4px; cursor: pointer; width: 100%; font-size: 14px;">
                    🚀 Load Working Sidebar
                </button>
            </div>
            
            <div style="margin-top: 15px; font-size: 11px; opacity: 0.7; text-align: center;">
                If you see this sidebar, the basic extension is working!
            </div>
        `;
        
        // Add to page
        document.body.appendChild(sidebar);
        console.log('✅ DEBUG: Sidebar created and added to page');
        
        // Test background script connection
        window.testConnection = function() {
            console.log('🔄 DEBUG: Testing background script connection...');

            try {
                chrome.runtime.sendMessage({action: 'ping'}, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('❌ DEBUG: Connection error:', chrome.runtime.lastError.message);
                        alert('Background script not responding: ' + chrome.runtime.lastError.message);
                    } else {
                        console.log('✅ DEBUG: Background script responded:', response);
                        alert('✅ Background script is working!');
                    }
                });
            } catch (error) {
                console.error('❌ DEBUG: Exception testing connection:', error);
                alert('Exception: ' + error.message);
            }
        };

        // Make load function available
        window.loadFullExtension = function() {
            console.log('🔄 DEBUG: Loading full extension...');
            sidebar.remove();

            // Create a simple working sidebar instead of loading complex script
            createSimpleSidebar();
        };

        function createSimpleSidebar() {
            const simpleSidebar = document.createElement('div');
            simpleSidebar.id = 'simple-kaggle-sidebar';
            simpleSidebar.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                width: 350px !important;
                height: 100vh !important;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                z-index: 999999 !important;
                padding: 20px !important;
                box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5) !important;
                font-family: Arial, sans-serif !important;
                overflow-y: auto !important;
            `;

            simpleSidebar.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h2 style="margin: 0; font-size: 18px;">🤖 Kaggle Assistant</h2>
                    <p style="margin: 5px 0; font-size: 12px; opacity: 0.8;">Simple working version</p>
                </div>

                <div style="margin-bottom: 15px;">
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: #f44336; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        ❌ Close
                    </button>
                </div>

                <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                    <h3 style="margin: 0 0 10px 0; font-size: 14px;">📊 Quick Actions</h3>
                    <button onclick="analyzeCurrentCell()"
                            style="background: #4CAF50; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%; margin-bottom: 8px;">
                        🔍 Analyze Current Cell
                    </button>
                    <button onclick="showCommonImports()"
                            style="background: #2196F3; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%; margin-bottom: 8px;">
                        📦 Common Imports
                    </button>
                    <button onclick="showDataSummary()"
                            style="background: #FF9800; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%;">
                        📋 Data Summary Template
                    </button>
                </div>

                <div id="results-area" style="background: rgba(0,0,0,0.2); padding: 15px; border-radius: 8px; min-height: 100px;">
                    <p style="margin: 0; text-align: center; opacity: 0.7;">Click a button above to see results</p>
                </div>
            `;

            document.body.appendChild(simpleSidebar);

            // Add simple functionality
            window.analyzeCurrentCell = function() {
                const results = document.getElementById('results-area');
                const cells = document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]');
                results.innerHTML = `
                    <h4 style="margin: 0 0 10px 0;">📊 Analysis Results</h4>
                    <p>Cells found: ${cells.length}</p>
                    <p>Page loaded: ${document.readyState}</p>
                    <p>Extension working: ✅ Yes</p>
                `;
            };

            window.showCommonImports = function() {
                const results = document.getElementById('results-area');
                const imports = `import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split`;

                results.innerHTML = `
                    <h4 style="margin: 0 0 10px 0;">📦 Common Imports</h4>
                    <pre style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">${imports}</pre>
                    <button onclick="navigator.clipboard.writeText('${imports}')"
                            style="background: #4CAF50; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-top: 8px;">
                        📋 Copy
                    </button>
                `;
            };

            window.showDataSummary = function() {
                const results = document.getElementById('results-area');
                const template = `# Data Summary
print("Dataset shape:", df.shape)
print("\\nColumn info:")
print(df.info())
print("\\nStatistical summary:")
print(df.describe())`;

                results.innerHTML = `
                    <h4 style="margin: 0 0 10px 0;">📋 Data Summary</h4>
                    <pre style="background: rgba(0,0,0,0.3); padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;">${template}</pre>
                    <button onclick="navigator.clipboard.writeText('${template}')"
                            style="background: #4CAF50; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-top: 8px;">
                        📋 Copy
                    </button>
                `;
            };
        }
    }
    
    // Start the process
    waitForPage();
    
})();
