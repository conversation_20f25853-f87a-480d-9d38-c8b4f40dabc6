# Troubleshooting Guide - <PERSON>ggle Notebook Assistant

## 🚨 **Sidebar Not Appearing - Quick Fix**

### **Step 1: Test Basic Functionality**

1. **Reload the extension**:
   - Go to `chrome://extensions/`
   - Find "Kaggle Notebook Assistant"
   - Click the reload button (🔄)

2. **Check you're on the right page**:
   - Must be on `https://www.kaggle.com/code/...`
   - NOT just `https://www.kaggle.com/`
   - Must be an actual notebook page

3. **Test with simple version**:
   - The extension now loads a test sidebar first
   - You should see a purple "🤖 Test Sidebar" on the right
   - If this doesn't appear, there's a basic loading issue

### **Step 2: Debug Information**

Open browser console (F12) and look for these messages:
```
🤖 Kaggle Assistant: Test content script loaded
✅ On Kaggle domain
✅ On Kaggle notebook page
🚀 Creating test sidebar...
✅ Test sidebar created successfully
```

### **Step 3: Common Issues & Solutions**

#### ❌ **Extension Not Loading**
**Symptoms**: No console messages at all
**Solutions**:
1. Check extension is enabled in `chrome://extensions/`
2. Verify all files are in the correct folder
3. Look for errors in the Extensions page
4. Try reloading the extension

#### ❌ **Wrong Page Type**
**Symptoms**: "Not on notebook page" in console
**Solutions**:
1. Navigate to an actual Kaggle notebook
2. URL must contain `/code/`
3. Try creating a new notebook
4. Refresh the page after navigating

#### ❌ **Content Script Blocked**
**Symptoms**: Extension loads but no sidebar
**Solutions**:
1. Check if other extensions are interfering
2. Disable ad blockers temporarily
3. Check Chrome's site permissions
4. Try incognito mode

#### ❌ **CSS/Styling Issues**
**Symptoms**: Sidebar appears but looks broken
**Solutions**:
1. Check browser zoom level (should be 100%)
2. Try different screen resolution
3. Check for CSS conflicts with other extensions
4. Clear browser cache

## 🔧 **Detailed Debugging Steps**

### **1. Extension Installation Check**
```bash
# Check these files exist in your extension folder:
manifest.json
test-content.js
content.js
background.js
sidebar.css
popup.html
popup.js
settings.html
settings.js
```

### **2. Browser Console Debugging**
1. Open Kaggle notebook page
2. Press F12 to open DevTools
3. Go to Console tab
4. Look for messages starting with "🤖"
5. Check for any red error messages

### **3. Extension Console Debugging**
1. Go to `chrome://extensions/`
2. Find "Kaggle Notebook Assistant"
3. Click "Inspect views: service worker"
4. Check for errors in the background script

### **4. Network Issues**
1. Check if you can access Kaggle normally
2. Verify no corporate firewall blocking
3. Try different network if possible
4. Check Chrome's site permissions for Kaggle

## 🛠️ **Manual Testing Steps**

### **Test 1: Basic Extension Loading**
1. Go to `chrome://extensions/`
2. Verify extension is enabled and loaded
3. Check for any error messages
4. Try reloading the extension

### **Test 2: Page Detection**
1. Navigate to `https://www.kaggle.com/code`
2. Open any notebook or create new one
3. Open browser console (F12)
4. Look for "Kaggle Assistant" messages

### **Test 3: Content Script Injection**
1. On a Kaggle notebook page
2. Open console and type: `console.log('Test:', document.getElementById('test-kaggle-sidebar'))`
3. Should return the sidebar element or null

### **Test 4: Manual Sidebar Creation**
1. On Kaggle notebook page
2. Open console and paste:
```javascript
const sidebar = document.createElement('div');
sidebar.style.cssText = 'position:fixed;top:0;right:0;width:300px;height:100px;background:red;z-index:10000;';
sidebar.textContent = 'Manual Test Sidebar';
document.body.appendChild(sidebar);
```
3. Red box should appear on right side

## 🔍 **Advanced Debugging**

### **Check Content Script Injection**
```javascript
// Run in console on Kaggle page
chrome.runtime.sendMessage({action: 'ping'}, response => {
    console.log('Extension response:', response);
});
```

### **Check Page Elements**
```javascript
// Run in console to check page structure
console.log('Cells:', document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]').length);
console.log('Notebook:', document.querySelector('.notebook-container, [data-testid="notebook-container"]'));
console.log('URL:', window.location.href);
```

### **Force Sidebar Creation**
```javascript
// Run in console to manually create sidebar
if (window.createTestSidebar) {
    window.createTestSidebar();
} else {
    console.log('createTestSidebar function not available');
}
```

## 📋 **Troubleshooting Checklist**

### **Before Reporting Issues**
- [ ] Extension is enabled in Chrome
- [ ] On correct Kaggle notebook URL
- [ ] Browser console shows no errors
- [ ] Tried reloading extension
- [ ] Tested in incognito mode
- [ ] No other extensions interfering

### **Information to Provide**
When reporting issues, include:
1. **Chrome version**: `chrome://version/`
2. **Extension version**: Check in `chrome://extensions/`
3. **Kaggle URL**: The specific notebook URL
4. **Console messages**: Copy all messages from F12 console
5. **Screenshots**: Show the issue visually
6. **Steps taken**: What you tried before reporting

## 🚀 **Quick Fixes**

### **Fix 1: Reload Everything**
1. Reload extension in `chrome://extensions/`
2. Refresh Kaggle page
3. Wait 5 seconds for loading

### **Fix 2: Reset Extension**
1. Remove extension
2. Restart Chrome
3. Reinstall extension
4. Test on fresh Kaggle page

### **Fix 3: Clean Install**
1. Download fresh copy of extension files
2. Remove old extension
3. Clear browser cache
4. Install fresh copy

### **Fix 4: Alternative Loading**
If automatic loading fails, try:
1. Click extension icon in toolbar
2. Click "Toggle Sidebar" button
3. Or use keyboard shortcut Ctrl+Shift+K

## 📞 **Getting Help**

### **Self-Help Resources**
1. Check this troubleshooting guide
2. Review INSTALLATION.md
3. Check GitHub Issues for similar problems
4. Try the test sidebar first

### **Reporting Issues**
1. Create GitHub Issue with "Bug" label
2. Include all debugging information
3. Attach screenshots if helpful
4. Describe exact steps to reproduce

### **Emergency Workaround**
If nothing works, you can manually inject the sidebar:
1. Go to Kaggle notebook
2. Open console (F12)
3. Paste the manual sidebar creation code above
4. This creates a basic sidebar for immediate use

---

**Remember**: The test sidebar should appear immediately. If it doesn't, the issue is with basic extension loading, not the advanced features.
