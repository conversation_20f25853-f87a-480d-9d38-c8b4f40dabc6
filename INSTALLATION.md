# Installation & Setup Guide

## Quick Installation

### Method 1: Load Unpacked Extension (Recommended for Development)

1. **Download the Extension**
   - Clone or download this repository to your local machine
   - Extract to a folder like `C:\kaggle-assistant\` or `~/kaggle-assistant/`

2. **Open Chrome Extensions**
   - Open Google Chrome
   - Navigate to `chrome://extensions/`
   - Or click the three dots menu → More tools → Extensions

3. **Enable Developer Mode**
   - Toggle the "Developer mode" switch in the top right corner
   - This enables the "Load unpacked" button

4. **Load the Extension**
   - Click "Load unpacked"
   - Select the folder containing the extension files
   - The extension should appear in your extensions list

5. **Pin the Extension**
   - Click the puzzle piece icon in the Chrome toolbar
   - Find "Kaggle Notebook Assistant" and click the pin icon
   - The extension icon will now appear in your toolbar

### Method 2: Chrome Web Store (Coming Soon)
The extension will be available on the Chrome Web Store once published.

## Verification

### Check Installation
1. Look for the 🤖 icon in your Chrome toolbar
2. Click the icon - you should see a popup with status information
3. Navigate to any Kaggle notebook page
4. The sidebar should appear automatically on the right side

### Test Basic Functionality
1. Go to [Kaggle Notebooks](https://www.kaggle.com/code)
2. Open any notebook or create a new one
3. Verify the sidebar appears with "🤖 Kaggle Assistant" header
4. Click any button in the sidebar to test functionality

## Configuration

### Default Settings
The extension works out of the box with these defaults:
- **Auto-activate**: On all Kaggle notebook pages
- **Sidebar position**: Right side of the screen
- **Keyboard shortcuts**: Enabled
- **Auto-suggestions**: Enabled with 2-second delay

### Customization Options

#### Keyboard Shortcuts
- `Ctrl+Shift+K` - Toggle sidebar
- `Ctrl+Shift+A` - Analyze current cell
- `Ctrl+Shift+S` - Smart suggestions
- `Ctrl+Shift+D` - Debug current cell
- `Ctrl+Shift+O` - Optimize code
- `Ctrl+Shift+H` - Show help

#### Sidebar Behavior
- Click the minimize button (−) to collapse the sidebar
- Click the close button (×) to hide the sidebar
- Right-click any cell for context menu options

## Troubleshooting

### Extension Not Loading
**Problem**: Extension doesn't appear in Chrome
**Solutions**:
1. Ensure Developer mode is enabled
2. Check that all files are in the correct folder
3. Look for errors in the Extensions page
4. Try reloading the extension

### Sidebar Not Appearing
**Problem**: No sidebar on Kaggle notebook pages
**Solutions**:
1. Verify you're on a notebook page (URL contains `/code/`)
2. Refresh the page
3. Check browser console for errors (F12)
4. Try clicking the extension icon and "Toggle Sidebar"

### Features Not Working
**Problem**: Buttons don't respond or show errors
**Solutions**:
1. Check browser console for JavaScript errors
2. Ensure you have an active internet connection
3. Try refreshing the page
4. Reload the extension in `chrome://extensions/`

### Performance Issues
**Problem**: Slow response or high memory usage
**Solutions**:
1. Close other Chrome tabs to free memory
2. Restart Chrome browser
3. Check for Chrome updates
4. Disable other extensions temporarily

## Browser Requirements

### Minimum Requirements
- **Chrome**: Version 88 or higher
- **Edge**: Version 88 or higher (Chromium-based)
- **Memory**: 4GB RAM recommended
- **Storage**: 10MB free space

### Optimal Performance
- **Chrome**: Latest stable version
- **Memory**: 8GB+ RAM
- **CPU**: Modern multi-core processor
- **Display**: 1366x768 or higher resolution

## Permissions Explained

The extension requests these permissions:

### `activeTab`
- **Purpose**: Access the current Kaggle tab
- **Usage**: Read notebook content and inject the sidebar
- **Privacy**: Only accesses the active tab when you use the extension

### `storage`
- **Purpose**: Save user preferences
- **Usage**: Remember sidebar state and settings
- **Privacy**: All data stored locally in your browser

### `scripting`
- **Purpose**: Inject content scripts
- **Usage**: Add the sidebar and functionality to Kaggle pages
- **Privacy**: Only runs on Kaggle domains

### `host_permissions`
- **Domains**: `https://www.kaggle.com/*`
- **Purpose**: Only work on Kaggle websites
- **Privacy**: Cannot access any other websites

## Security & Privacy

### Data Protection
- ✅ **No data collection**: Extension doesn't collect personal information
- ✅ **Local processing**: All analysis happens in your browser
- ✅ **No external requests**: No data sent to external servers
- ✅ **Open source**: Full code is available for review

### Safe Usage
- The extension only activates on Kaggle domains
- Your notebook code stays in your browser
- No tracking or analytics
- No ads or monetization

## Updates

### Automatic Updates (Chrome Web Store)
When published to Chrome Web Store:
- Updates will install automatically
- You'll be notified of major changes
- Settings and preferences will be preserved

### Manual Updates (Development)
For the unpacked extension:
1. Download the latest version
2. Replace the old files
3. Go to `chrome://extensions/`
4. Click the reload button for the extension

## Uninstallation

### Remove Extension
1. Go to `chrome://extensions/`
2. Find "Kaggle Notebook Assistant"
3. Click "Remove"
4. Confirm removal

### Clean Removal
1. Remove the extension as above
2. Clear browser data if desired:
   - Go to `chrome://settings/clearBrowserData`
   - Select "Advanced" tab
   - Choose "All time" for time range
   - Check "Site data" and "Cached images and files"
   - Click "Clear data"

## Support

### Getting Help
1. **Check Documentation**: Read README.md and DEVELOPMENT.md
2. **Test Installation**: Follow TESTING.md checklist
3. **Report Issues**: Use GitHub Issues for bug reports
4. **Community**: Join discussions for feature requests

### Common Issues Database

#### Issue: "Extension not working on new Kaggle layout"
**Solution**: The extension adapts to Kaggle UI changes. If it stops working:
1. Refresh the page
2. Check for extension updates
3. Report the issue with screenshots

#### Issue: "Sidebar overlaps with Kaggle interface"
**Solution**: 
1. Try minimizing the sidebar
2. Adjust your browser zoom level
3. Use a larger screen resolution

#### Issue: "Code suggestions not relevant"
**Solution**:
1. Ensure you're in a code cell
2. Try typing more context
3. Use the "Analyze All Cells" feature for better context

### Performance Tips
1. **Close unused tabs** to free memory
2. **Use latest Chrome** for best performance
3. **Restart browser** if experiencing slowdowns
4. **Check system resources** if issues persist

## Advanced Configuration

### For Developers
If you want to modify the extension:

1. **Edit source files** in your local copy
2. **Reload extension** in `chrome://extensions/`
3. **Test changes** on Kaggle notebooks
4. **Check console** for any errors

### Custom Shortcuts
To change keyboard shortcuts:
1. Go to `chrome://extensions/shortcuts`
2. Find "Kaggle Notebook Assistant"
3. Click the pencil icon to edit shortcuts
4. Set your preferred key combinations

---

## Quick Reference Card

### Essential Actions
| Action | Method |
|--------|--------|
| Toggle Sidebar | Click extension icon → "Toggle Sidebar" |
| Analyze Code | `Ctrl+Shift+A` or click "Analyze Current Data" |
| Get Suggestions | `Ctrl+Shift+S` or click "Get Code Suggestions" |
| Debug Errors | `Ctrl+Shift+D` or click "Debug Current Error" |
| Quick Help | `Ctrl+Shift+H` |

### Quick Start
1. Install extension
2. Go to Kaggle notebook
3. See sidebar on right
4. Click any tool to start

**Need help?** Check the documentation or report issues on GitHub!
