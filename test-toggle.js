// Simple test script to verify toggle functionality
(function() {
    'use strict';
    
    console.log('🧪 TEST: Toggle test script loaded');
    
    // Create a simple test sidebar that responds to toggle
    function createTestToggleSidebar() {
        const sidebar = document.createElement('div');
        sidebar.id = 'test-toggle-sidebar';
        sidebar.style.cssText = `
            position: fixed !important;
            top: 50px !important;
            right: 20px !important;
            width: 250px !important;
            height: 200px !important;
            background: #4CAF50 !important;
            color: white !important;
            z-index: 999999 !important;
            padding: 20px !important;
            border-radius: 8px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
            font-family: Arial, sans-serif !important;
        `;
        
        sidebar.innerHTML = `
            <h3 style="margin: 0 0 10px 0;">🧪 Toggle Test</h3>
            <p style="margin: 0 0 10px 0; font-size: 12px;">This sidebar responds to the toggle button!</p>
            <button id="close-test-toggle" style="background: #f44336; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
                Close
            </button>
            <div style="margin-top: 15px; font-size: 11px; opacity: 0.8;">
                Try clicking the extension icon and "Toggle Sidebar"
            </div>
        `;
        
        document.body.appendChild(sidebar);
        
        // Add close button functionality
        document.getElementById('close-test-toggle').addEventListener('click', () => {
            sidebar.remove();
        });
        
        console.log('🧪 TEST: Toggle test sidebar created');
        return sidebar;
    }
    
    // Listen for toggle messages
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('🧪 TEST: Message received:', request);
        
        if (request.action === 'toggleSidebar') {
            const existingSidebar = document.getElementById('test-toggle-sidebar');
            
            if (existingSidebar) {
                // Toggle visibility
                if (existingSidebar.style.display === 'none') {
                    existingSidebar.style.display = 'block';
                    console.log('🧪 TEST: Sidebar shown');
                    sendResponse({ success: true, action: 'shown' });
                } else {
                    existingSidebar.style.display = 'none';
                    console.log('🧪 TEST: Sidebar hidden');
                    sendResponse({ success: true, action: 'hidden' });
                }
            } else {
                // Create new sidebar
                console.log('🧪 TEST: Creating new sidebar');
                createTestToggleSidebar();
                sendResponse({ success: true, action: 'created' });
            }
            
            return true; // Keep message channel open
        }
        
        sendResponse({ success: false, error: 'Unknown action' });
        return false;
    });
    
    // Auto-create sidebar for immediate testing
    if (window.location.href.includes('kaggle.com/code/')) {
        console.log('🧪 TEST: Auto-creating test sidebar');
        setTimeout(createTestToggleSidebar, 1000);
    }
    
})();
