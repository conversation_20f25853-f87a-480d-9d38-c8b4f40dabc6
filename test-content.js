// Simple test version of content script with duplicate prevention
(function() {
    'use strict';

    // Prevent multiple injections
    if (window.kaggleAssistantTestLoaded) {
        console.log('🔄 Kaggle Assistant: Test script already loaded, skipping');
        return;
    }

    window.kaggleAssistantTestLoaded = true;
    console.log('🤖 Kaggle Assistant: Test content script loaded');

    // Check if we're on the right page
    if (window.location.href.includes('kaggle.com')) {
        console.log('✅ On Kaggle domain');

        if (window.location.href.includes('/code/')) {
            console.log('✅ On Kaggle notebook page');
            // Wait a bit for page to fully load
            setTimeout(createTestSidebar, 1000);
        } else {
            console.log('❌ Not on notebook page, current URL:', window.location.href);
        }
    } else {
        console.log('❌ Not on Kaggle domain');
    }

function createTestSidebar() {
    console.log('🚀 Creating test sidebar...');
    
    // Remove any existing sidebar
    const existing = document.getElementById('test-kaggle-sidebar');
    if (existing) {
        existing.remove();
    }
    
    // Create simple sidebar
    const sidebar = document.createElement('div');
    sidebar.id = 'test-kaggle-sidebar';
    sidebar.style.cssText = `
        position: fixed;
        top: 0;
        right: 0;
        width: 300px;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        z-index: 10000;
        padding: 20px;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
        font-family: Arial, sans-serif;
        overflow-y: auto;
    `;
    
    sidebar.innerHTML = `
        <div style="margin-bottom: 20px;">
            <h2 style="margin: 0; font-size: 18px;">🤖 Test Sidebar</h2>
            <p style="margin: 5px 0; font-size: 12px; opacity: 0.8;">Extension is working!</p>
        </div>
        
        <div style="margin-bottom: 15px;">
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="background: #f44336; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                ❌ Close
            </button>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 15px;">
            <h3 style="margin: 0 0 10px 0; font-size: 14px;">Debug Info:</h3>
            <p style="margin: 5px 0; font-size: 12px;">URL: ${window.location.href}</p>
            <p style="margin: 5px 0; font-size: 12px;">Domain: ${window.location.hostname}</p>
            <p style="margin: 5px 0; font-size: 12px;">Path: ${window.location.pathname}</p>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
            <h3 style="margin: 0 0 10px 0; font-size: 14px;">Page Elements:</h3>
            <p style="margin: 5px 0; font-size: 12px;">Cells found: ${document.querySelectorAll('.cell, .code-cell, [data-testid="code-cell"]').length}</p>
            <p style="margin: 5px 0; font-size: 12px;">Notebook container: ${document.querySelector('.notebook-container, [data-testid="notebook-container"]') ? 'Found' : 'Not found'}</p>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testFullSidebar()" 
                    style="background: #4CAF50; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%;">
                🚀 Load Full Sidebar
            </button>
        </div>
    `;
    
    document.body.appendChild(sidebar);
    console.log('✅ Test sidebar created successfully');
}

function testFullSidebar() {
    console.log('🔄 Loading full sidebar...');
    
    // Remove test sidebar
    const testSidebar = document.getElementById('test-kaggle-sidebar');
    if (testSidebar) {
        testSidebar.remove();
    }
    
    // Try to load the full extension
    try {
        if (window.KaggleNotebookIntegration) {
            new window.KaggleNotebookIntegration();
        } else {
            // Fallback: inject the main content script
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('content.js');
            document.head.appendChild(script);
        }
    } catch (error) {
        console.error('Error loading full sidebar:', error);
        alert('Error loading full sidebar. Check console for details.');
    }
}

    // Make function globally available
    window.testFullSidebar = testFullSidebar;

})(); // End of IIFE (Immediately Invoked Function Expression)
