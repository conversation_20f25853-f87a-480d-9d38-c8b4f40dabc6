# Development Guide - Kaggle Notebook Assistant

## Quick Start

### 1. Load the Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked" and select this project folder
4. The extension should now appear in your extensions list

### 2. Test the Extension
1. Navigate to any Kaggle notebook: `https://www.kaggle.com/code/`
2. Open any notebook or create a new one
3. Click the extension icon in the toolbar
4. Click "Toggle Sidebar" to show/hide the assistant

## Project Structure

```
kaggle-assistant/
├── manifest.json          # Extension manifest (Manifest V3)
├── background.js          # Service worker for background tasks
├── content.js            # Main content script injected into Kaggle pages
├── sidebar.css           # Styles for the sidebar interface
├── popup.html            # Extension popup HTML
├── popup.js              # Popup functionality
├── icons/                # Extension icons
│   └── icon.svg          # Main icon (SVG format)
├── package.json          # NPM package configuration
├── README.md             # User documentation
└── DEVELOPMENT.md        # This file
```

## Key Components

### 1. Manifest (manifest.json)
- **Version**: Manifest V3 (latest Chrome extension format)
- **Permissions**: Minimal permissions for security
- **Host Permissions**: Only Kaggle.com domains
- **Content Scripts**: Auto-inject on Kaggle notebook pages

### 2. Background Script (background.js)
- **Service Worker**: Handles background tasks
- **Message Handling**: Processes requests from content script
- **Code Analysis**: Basic code analysis functions
- **Suggestions**: Generates code suggestions and tips

### 3. Content Script (content.js)
- **Sidebar Creation**: Dynamically creates the assistant sidebar
- **Event Handling**: Manages user interactions
- **Notebook Integration**: Reads current cell content
- **UI Updates**: Updates results and suggestions

### 4. Popup (popup.html/js)
- **Status Display**: Shows if extension is active
- **Quick Actions**: Toggle sidebar, open Kaggle
- **User Interface**: Clean, branded popup design

## Development Workflow

### Making Changes

1. **Edit Files**: Modify any source files as needed
2. **Reload Extension**: Go to `chrome://extensions/` and click reload
3. **Test Changes**: Navigate to a Kaggle notebook and test
4. **Debug**: Use Chrome DevTools for debugging

### Debugging

#### Content Script Debugging
1. Open Kaggle notebook page
2. Press F12 to open DevTools
3. Check Console tab for errors
4. Use Sources tab to set breakpoints in content.js

#### Background Script Debugging
1. Go to `chrome://extensions/`
2. Click "Inspect views: service worker" under your extension
3. Debug background.js in the opened DevTools

#### Popup Debugging
1. Right-click the extension icon
2. Select "Inspect popup"
3. Debug popup.js in the opened DevTools

### Common Development Tasks

#### Adding New Analysis Features
1. Add function to `background.js`
2. Add UI button to content script
3. Handle message passing between scripts
4. Update results display

#### Modifying Sidebar Appearance
1. Edit `sidebar.css` for styling changes
2. Modify HTML structure in `content.js`
3. Test responsive design on different screen sizes

#### Adding New Code Templates
1. Extend suggestion functions in `background.js`
2. Add new buttons/sections to sidebar
3. Implement copy-to-clipboard functionality

## Testing

### Manual Testing Checklist
- [ ] Extension loads without errors
- [ ] Sidebar appears on Kaggle notebook pages
- [ ] All buttons respond correctly
- [ ] Code analysis works with sample code
- [ ] Suggestions are relevant and helpful
- [ ] Copy functionality works
- [ ] Responsive design on different screen sizes

### Test Scenarios
1. **Fresh Notebook**: Test on a new, empty notebook
2. **Existing Code**: Test with notebooks containing various code types
3. **Different Data Types**: Test with pandas, numpy, matplotlib code
4. **Error Handling**: Test with invalid/broken code
5. **Performance**: Test with large notebooks

## Architecture Decisions

### Why Manifest V3?
- **Future-proof**: Google's latest extension format
- **Security**: Enhanced security model
- **Performance**: Better resource management
- **Service Workers**: More reliable background processing

### Why Sidebar Approach?
- **Non-intrusive**: Doesn't interfere with Kaggle's UI
- **Persistent**: Stays available while working
- **Contextual**: Can analyze current cell content
- **Expandable**: Easy to add new features

### Why Local Processing?
- **Privacy**: No data sent to external servers
- **Speed**: Instant analysis and suggestions
- **Reliability**: Works offline
- **Security**: User code stays in browser

## Extension APIs Used

### Chrome APIs
- `chrome.runtime`: Message passing and extension lifecycle
- `chrome.tabs`: Tab management and URL detection
- `chrome.scripting`: Content script injection
- `chrome.storage`: User preferences (future use)

### Web APIs
- `MutationObserver`: Detect notebook changes
- `document.querySelector`: DOM manipulation
- `navigator.clipboard`: Copy functionality
- `fetch`: Future API integrations

## Performance Considerations

### Optimization Strategies
1. **Lazy Loading**: Only create sidebar when needed
2. **Event Delegation**: Efficient event handling
3. **Debouncing**: Limit analysis frequency
4. **Memory Management**: Clean up observers and listeners

### Resource Usage
- **Minimal DOM Impact**: Sidebar is isolated
- **Efficient CSS**: Optimized selectors and animations
- **Small Bundle Size**: No external dependencies
- **Fast Startup**: Quick initialization

## Security Considerations

### Data Privacy
- **No External Requests**: All processing is local
- **Minimal Permissions**: Only necessary permissions requested
- **Kaggle-Only**: Restricted to Kaggle domains
- **No Data Storage**: No persistent user data collection

### Code Safety
- **Input Sanitization**: Safe handling of user code
- **XSS Prevention**: Proper HTML escaping
- **CSP Compliance**: Content Security Policy adherence

## Future Enhancements

### Planned Features
1. **AI Integration**: Connect to AI APIs for advanced suggestions
2. **Custom Templates**: User-defined code templates
3. **Export Features**: Save analysis results
4. **Collaboration**: Share suggestions with team members

### Technical Improvements
1. **TypeScript**: Add type safety
2. **Testing Framework**: Automated testing
3. **Build Process**: Webpack/Rollup integration
4. **CI/CD**: Automated deployment pipeline

## Troubleshooting

### Common Issues

#### Sidebar Not Appearing
- Check if on correct URL (`/code/` path required)
- Verify content script injection
- Check console for JavaScript errors

#### Features Not Working
- Ensure extension is enabled
- Check background script status
- Verify message passing between scripts

#### Styling Issues
- Check CSS conflicts with Kaggle's styles
- Verify z-index values
- Test on different screen resolutions

### Debug Commands

```javascript
// Check if content script is loaded
console.log('Kaggle Assistant:', window.kaggleAssistant);

// Manually trigger sidebar
document.dispatchEvent(new CustomEvent('kaggle-assistant-toggle'));

// Check extension status
chrome.runtime.sendMessage({action: 'ping'}, console.log);
```

## Contributing

### Code Style
- Use ES6+ features
- Follow consistent indentation (2 spaces)
- Add comments for complex logic
- Use meaningful variable names

### Pull Request Process
1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Update documentation
5. Submit pull request

---

Happy coding! 🚀
