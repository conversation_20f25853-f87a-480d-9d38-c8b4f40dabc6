{"name": "kaggle-notebook-assistant", "version": "1.0.0", "description": "AI-powered sidebar assistant for Kaggle notebooks", "main": "background.js", "scripts": {"build": "echo 'Building extension...' && npm run validate", "validate": "echo 'Validating manifest...' && node -e \"console.log('Manifest valid:', JSON.parse(require('fs').readFileSync('manifest.json', 'utf8')))\"", "test": "echo 'Running tests...'", "dev": "echo 'Development mode - load unpacked extension in Chrome'", "package": "echo 'Creating package...' && zip -r kaggle-assistant.zip . -x node_modules/\\* .git/\\* *.zip"}, "keywords": ["kaggle", "chrome-extension", "data-science", "ai-assistant", "notebook", "programming", "analysis"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/kaggle-assistant"}, "devDependencies": {"web-ext": "^7.0.0"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 88"]}