// Settings page functionality
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadUsageStats();
});

async function loadSettings() {
    try {
        // Load API key status
        const result = await chrome.storage.local.get(['openai_api_key', 'extension_settings']);
        
        if (result.openai_api_key) {
            document.getElementById('apiKey').value = '••••••••••••••••••••••••••••••••••••••••••••••••••••';
            updateConnectionStatus(true, 'API key configured');
        } else {
            updateConnectionStatus(false, 'No API key configured');
        }
        
        // Load extension settings
        const settings = result.extension_settings || {
            autoSuggestions: true,
            keyboardShortcuts: true,
            contextMenus: true,
            suggestionDelay: 2
        };
        
        document.getElementById('autoSuggestions').checked = settings.autoSuggestions;
        document.getElementById('keyboardShortcuts').checked = settings.keyboardShortcuts;
        document.getElementById('contextMenus').checked = settings.contextMenus;
        document.getElementById('suggestionDelay').value = settings.suggestionDelay;
        
    } catch (error) {
        console.error('Error loading settings:', error);
        showStatus('Error loading settings', 'error');
    }
}

async function loadUsageStats() {
    try {
        const result = await chrome.storage.local.get(['usage_stats']);
        const stats = result.usage_stats || {
            suggestions: 0,
            analyses: 0,
            errors: 0
        };
        
        document.getElementById('suggestionsCount').textContent = stats.suggestions;
        document.getElementById('analysesCount').textContent = stats.analyses;
        document.getElementById('errorsCount').textContent = stats.errors;
    } catch (error) {
        console.error('Error loading usage stats:', error);
    }
}

function togglePassword() {
    const input = document.getElementById('apiKey');
    const btn = document.querySelector('.toggle-btn');
    
    if (input.type === 'password') {
        input.type = 'text';
        btn.textContent = '🙈';
    } else {
        input.type = 'password';
        btn.textContent = '👁️';
    }
}

async function saveSettings() {
    const apiKey = document.getElementById('apiKey').value.trim();
    
    if (!apiKey) {
        showStatus('Please enter an API key', 'error');
        return;
    }
    
    // Don't save if it's the masked value
    if (apiKey.includes('••••')) {
        showStatus('API key already saved', 'info');
        return;
    }
    
    // Validate API key format
    if (!apiKey.startsWith('sk-')) {
        showStatus('Invalid API key format. Should start with "sk-"', 'error');
        return;
    }
    
    try {
        await chrome.storage.local.set({ openai_api_key: apiKey });
        showStatus('API key saved successfully!', 'success');
        updateConnectionStatus(true, 'API key configured');
        
        // Mask the input
        document.getElementById('apiKey').value = '••••••••••••••••••••••••••••••••••••••••••••••••••••';
        
    } catch (error) {
        console.error('Error saving API key:', error);
        showStatus('Error saving API key', 'error');
    }
}

async function clearSettings() {
    if (!confirm('Are you sure you want to remove your API key?')) {
        return;
    }
    
    try {
        await chrome.storage.local.remove(['openai_api_key']);
        document.getElementById('apiKey').value = '';
        showStatus('API key removed', 'info');
        updateConnectionStatus(false, 'No API key configured');
    } catch (error) {
        console.error('Error clearing API key:', error);
        showStatus('Error clearing API key', 'error');
    }
}

async function testConnection() {
    const result = await chrome.storage.local.get(['openai_api_key']);
    const apiKey = result.openai_api_key;
    
    if (!apiKey) {
        showStatus('No API key configured', 'error');
        return;
    }
    
    showStatus('Testing connection...', 'info');
    updateConnectionStatus(false, 'Testing...');
    
    try {
        const response = await fetch('https://api.openai.com/v1/models', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            showStatus('✅ Connection successful!', 'success');
            updateConnectionStatus(true, 'Connected and working');
        } else {
            const errorData = await response.json();
            showStatus(`❌ Connection failed: ${errorData.error?.message || 'Unknown error'}`, 'error');
            updateConnectionStatus(false, 'Connection failed');
        }
    } catch (error) {
        console.error('Connection test error:', error);
        showStatus('❌ Connection test failed', 'error');
        updateConnectionStatus(false, 'Connection failed');
    }
}

async function saveExtensionSettings() {
    const settings = {
        autoSuggestions: document.getElementById('autoSuggestions').checked,
        keyboardShortcuts: document.getElementById('keyboardShortcuts').checked,
        contextMenus: document.getElementById('contextMenus').checked,
        suggestionDelay: parseInt(document.getElementById('suggestionDelay').value)
    };
    
    try {
        await chrome.storage.local.set({ extension_settings: settings });
        showStatus('Extension settings saved!', 'success');
        
        // Notify content scripts of settings change
        chrome.tabs.query({ url: 'https://www.kaggle.com/code/*' }, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, { 
                    action: 'settingsUpdated', 
                    settings: settings 
                });
            });
        });
    } catch (error) {
        console.error('Error saving extension settings:', error);
        showStatus('Error saving settings', 'error');
    }
}

async function resetStats() {
    if (!confirm('Are you sure you want to reset usage statistics?')) {
        return;
    }
    
    try {
        await chrome.storage.local.set({ 
            usage_stats: { suggestions: 0, analyses: 0, errors: 0 }
        });
        loadUsageStats();
        showStatus('Statistics reset', 'info');
    } catch (error) {
        console.error('Error resetting stats:', error);
        showStatus('Error resetting statistics', 'error');
    }
}

function updateConnectionStatus(connected, message) {
    const dot = document.getElementById('statusDot');
    const text = document.getElementById('statusText');
    
    if (connected) {
        dot.classList.add('connected');
        text.textContent = message;
        text.style.color = '#4CAF50';
    } else {
        dot.classList.remove('connected');
        text.textContent = message;
        text.style.color = '#f44336';
    }
}

function showStatus(message, type) {
    const statusDiv = document.getElementById('statusMessage');
    statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        statusDiv.innerHTML = '';
    }, 5000);
}

// Security warning for API key
document.getElementById('apiKey').addEventListener('focus', function() {
    if (!this.value || this.value.includes('••••')) {
        showStatus('⚠️ Never share your API key with anyone!', 'info');
    }
});

// Validate suggestion delay
document.getElementById('suggestionDelay').addEventListener('change', function() {
    const value = parseInt(this.value);
    if (value < 1) this.value = 1;
    if (value > 10) this.value = 10;
});
