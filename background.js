// Background script for Kaggle Notebook Assistant
chrome.runtime.onInstalled.addListener(() => {
  console.log('Kaggle Notebook Assistant installed');
});

// Listen for tab updates to detect Kaggle notebook pages
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('kaggle.com/code/')) {
    // Inject the sidebar when on a Kaggle notebook page
    chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['content.js']
    });
  }
});

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'analyzeCode') {
    // Handle code analysis requests
    analyzeCode(request.code, request.metadata).then(result => {
      sendResponse({ success: true, analysis: result });
    }).catch(error => {
      sendResponse({ success: false, error: error.message });
    });
    return true; // Keep message channel open for async response
  }

  if (request.action === 'getCodeSuggestions') {
    // Handle code suggestion requests
    getCodeSuggestions(request.context).then(suggestions => {
      sendResponse({ success: true, suggestions: suggestions });
    }).catch(error => {
      sendResponse({ success: false, error: error.message });
    });
    return true;
  }
});

// Enhanced code analysis function with optional AI integration
async function analyzeCode(code, metadata = {}) {
  try {
    // Local analysis (always available)
    const localAnalysis = {
      complexity: calculateComplexity(code),
      suggestions: getAdvancedSuggestions(code, metadata),
      patterns: detectAdvancedPatterns(code),
      quality: assessCodeQuality(code),
      performance: analyzePerformance(code),
      security: checkSecurity(code),
      dataFlow: analyzeDataFlow(code)
    };

    // Try AI-enhanced analysis if API key is configured
    const aiAnalysis = await getAIAnalysis(code, metadata);

    // Merge local and AI analysis
    return {
      ...localAnalysis,
      aiSuggestions: aiAnalysis.suggestions || [],
      aiInsights: aiAnalysis.insights || [],
      confidence: aiAnalysis.confidence || 'local-only'
    };
  } catch (error) {
    console.error('Analysis error:', error);
    return {
      complexity: { lines: 0, functions: 0, classes: 0 },
      suggestions: ['Error during analysis'],
      patterns: [],
      quality: { score: 0, issues: ['Analysis failed'] },
      performance: { score: 0, bottlenecks: [] },
      security: { score: 100, issues: [] },
      dataFlow: { steps: [] },
      aiSuggestions: [],
      aiInsights: [],
      confidence: 'error'
    };
  }
}

// AI-enhanced analysis (optional)
async function getAIAnalysis(code, metadata = {}) {
  try {
    // Check if user has configured API key in extension storage
    const result = await chrome.storage.local.get(['openai_api_key']);
    const apiKey = result.openai_api_key;

    if (!apiKey) {
      console.log('No OpenAI API key configured - using local analysis only');
      return { suggestions: [], insights: [], confidence: 'local-only' };
    }

    // Prepare prompt for OpenAI
    const prompt = createAnalysisPrompt(code, metadata);

    // Call OpenAI API
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert data scientist and Python programmer. Analyze the provided code and give specific, actionable suggestions for improvement.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0]?.message?.content || '';

    return parseAIResponse(aiResponse);

  } catch (error) {
    console.error('AI analysis error:', error);
    return { suggestions: [], insights: [], confidence: 'error' };
  }
}

function createAnalysisPrompt(code, metadata) {
  return `
Analyze this ${metadata.language || 'Python'} code from a Kaggle ${metadata.notebookType || 'notebook'}:

\`\`\`python
${code}
\`\`\`

Context:
- Notebook type: ${metadata.notebookType || 'general'}
- Cell count: ${metadata.cellCount || 'unknown'}
- Has data: ${metadata.hasData || false}
- Has model: ${metadata.hasModel || false}

Please provide:
1. 3-5 specific code improvement suggestions
2. 2-3 key insights about the code quality
3. Any potential issues or optimizations

Format as JSON:
{
  "suggestions": ["suggestion 1", "suggestion 2", ...],
  "insights": ["insight 1", "insight 2", ...],
  "issues": ["issue 1", "issue 2", ...]
}
`;
}

function parseAIResponse(response) {
  try {
    // Try to extract JSON from the response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0]);
      return {
        suggestions: parsed.suggestions || [],
        insights: parsed.insights || [],
        issues: parsed.issues || [],
        confidence: 'ai-enhanced'
      };
    }

    // Fallback: parse as plain text
    const lines = response.split('\n').filter(line => line.trim());
    return {
      suggestions: lines.slice(0, 3),
      insights: ['AI analysis completed'],
      issues: [],
      confidence: 'ai-basic'
    };
  } catch (error) {
    console.error('Error parsing AI response:', error);
    return {
      suggestions: ['AI analysis failed to parse'],
      insights: [],
      issues: [],
      confidence: 'error'
    };
  }
}

// Enhanced code suggestions function
async function getCodeSuggestions(context) {
  const suggestions = [];

  // Analyze context to provide relevant suggestions
  if (context && typeof context === 'string') {
    const contextLower = context.toLowerCase();

    // Data loading suggestions
    if (contextLower.includes('read_csv') || contextLower.includes('load')) {
      suggestions.push({
        type: 'data_loading',
        code: `# Enhanced data loading with error handling
try:
    df = pd.read_csv('/kaggle/input/data.csv')
    print(f"Data loaded successfully: {df.shape}")
    print(f"Columns: {df.columns.tolist()}")
except FileNotFoundError:
    print("File not found. Please check the path.")
except Exception as e:
    print(f"Error loading data: {e}")`,
        description: 'Robust data loading with error handling'
      });
    }

    // Visualization suggestions
    if (contextLower.includes('plot') || contextLower.includes('viz') || contextLower.includes('chart')) {
      suggestions.push({
        type: 'visualization',
        code: `# Comprehensive visualization setup
plt.style.use('seaborn-v0_8')
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Distribution plot
axes[0, 0].hist(df['column'], bins=30, alpha=0.7, color='skyblue')
axes[0, 0].set_title('Distribution')

# Box plot for outliers
axes[0, 1].boxplot(df['column'])
axes[0, 1].set_title('Box Plot')

# Correlation heatmap
sns.heatmap(df.corr(), annot=True, cmap='coolwarm', ax=axes[1, 0])
axes[1, 0].set_title('Correlation Matrix')

# Scatter plot
axes[1, 1].scatter(df['x'], df['y'], alpha=0.6)
axes[1, 1].set_title('Scatter Plot')

plt.tight_layout()
plt.show()`,
        description: 'Comprehensive visualization suite'
      });
    }

    // Machine learning suggestions
    if (contextLower.includes('model') || contextLower.includes('train') || contextLower.includes('predict')) {
      suggestions.push({
        type: 'machine_learning',
        code: `# Machine Learning Pipeline
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix

# Prepare data
X = df.drop('target', axis=1)
y = df['target']

# Split data
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.2, random_state=42, stratify=y
)

# Scale features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Train model with cross-validation
model = RandomForestClassifier(n_estimators=100, random_state=42)
cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5)
print(f"CV Score: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")

# Fit and evaluate
model.fit(X_train_scaled, y_train)
predictions = model.predict(X_test_scaled)
print(classification_report(y_test, predictions))`,
        description: 'Complete ML pipeline with validation'
      });
    }
  }

  // Default suggestions if no context or no matches
  if (suggestions.length === 0) {
    suggestions.push(
      {
        type: 'import',
        code: `# Essential data science imports
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")`,
        description: 'Essential data science imports'
      },
      {
        type: 'eda',
        code: `# Exploratory Data Analysis Template
def explore_dataset(df):
    """Comprehensive dataset exploration."""
    print("=== Dataset Overview ===")
    print(f"Shape: {df.shape}")
    print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

    print("\\n=== Data Types ===")
    print(df.dtypes.value_counts())

    print("\\n=== Missing Values ===")
    missing = df.isnull().sum()
    if missing.sum() > 0:
        missing_df = pd.DataFrame({
            'Missing': missing,
            'Percentage': (missing / len(df)) * 100
        })
        print(missing_df[missing_df['Missing'] > 0].sort_values('Missing', ascending=False))
    else:
        print("No missing values found!")

    print("\\n=== Statistical Summary ===")
    print(df.describe())

    return df

# Use the function
explore_dataset(df)`,
        description: 'Comprehensive EDA function'
      }
    );
  }

  return suggestions;
}

// Helper functions
function calculateComplexity(code) {
  const lines = code.split('\n').filter(line => line.trim().length > 0);
  return {
    lines: lines.length,
    functions: (code.match(/def\s+\w+/g) || []).length,
    classes: (code.match(/class\s+\w+/g) || []).length
  };
}

function getBasicSuggestions(code) {
  const suggestions = [];
  
  if (!code.includes('import pandas')) {
    suggestions.push('Consider importing pandas for data manipulation');
  }
  
  if (!code.includes('import numpy')) {
    suggestions.push('Consider importing numpy for numerical operations');
  }
  
  if (code.includes('for ') && !code.includes('enumerate')) {
    suggestions.push('Consider using enumerate() for cleaner loops');
  }
  
  return suggestions;
}

// Enhanced pattern detection
function detectAdvancedPatterns(code) {
  const patterns = [];

  // Data operations
  if (code.includes('pd.read_csv') || code.includes('pd.read_excel')) {
    patterns.push('Data loading detected');
  }

  if (code.includes('fillna') || code.includes('dropna') || code.includes('interpolate')) {
    patterns.push('Missing value handling detected');
  }

  if (code.includes('groupby') || code.includes('agg') || code.includes('pivot')) {
    patterns.push('Data aggregation detected');
  }

  // Visualization patterns
  if (code.includes('plt.') || code.includes('seaborn') || code.includes('sns.')) {
    patterns.push('Data visualization detected');
  }

  if (code.includes('plotly') || code.includes('bokeh')) {
    patterns.push('Interactive visualization detected');
  }

  // Machine learning patterns
  if (code.includes('sklearn') || code.includes('from sklearn')) {
    patterns.push('Scikit-learn usage detected');
  }

  if (code.includes('tensorflow') || code.includes('keras')) {
    patterns.push('Deep learning (TensorFlow) detected');
  }

  if (code.includes('torch') || code.includes('pytorch')) {
    patterns.push('Deep learning (PyTorch) detected');
  }

  if (code.includes('xgboost') || code.includes('lightgbm') || code.includes('catboost')) {
    patterns.push('Gradient boosting detected');
  }

  // Feature engineering
  if (code.includes('LabelEncoder') || code.includes('OneHotEncoder') || code.includes('get_dummies')) {
    patterns.push('Categorical encoding detected');
  }

  if (code.includes('StandardScaler') || code.includes('MinMaxScaler') || code.includes('normalize')) {
    patterns.push('Feature scaling detected');
  }

  // Model evaluation
  if (code.includes('cross_val_score') || code.includes('GridSearchCV') || code.includes('RandomizedSearchCV')) {
    patterns.push('Model validation detected');
  }

  if (code.includes('classification_report') || code.includes('confusion_matrix') || code.includes('roc_auc_score')) {
    patterns.push('Model evaluation detected');
  }

  // Time series
  if (code.includes('datetime') || code.includes('pd.to_datetime') || code.includes('resample')) {
    patterns.push('Time series analysis detected');
  }

  // Statistical analysis
  if (code.includes('scipy.stats') || code.includes('statsmodels')) {
    patterns.push('Statistical analysis detected');
  }

  return patterns;
}

// Advanced suggestion system
function getAdvancedSuggestions(code, metadata = {}) {
  const suggestions = [];

  // Code quality suggestions
  if (!code.includes('import pandas')) {
    suggestions.push('Consider importing pandas for data manipulation');
  }

  if (!code.includes('import numpy')) {
    suggestions.push('Consider importing numpy for numerical operations');
  }

  if (code.includes('for ') && !code.includes('enumerate') && !code.includes('range')) {
    suggestions.push('Consider using enumerate() or vectorized operations instead of basic loops');
  }

  if (code.includes('pd.read_csv') && !code.includes('dtype')) {
    suggestions.push('Specify data types when reading CSV for better performance');
  }

  if (code.includes('plt.') && !code.includes('plt.figure(figsize')) {
    suggestions.push('Set figure size for better visualization: plt.figure(figsize=(10, 6))');
  }

  // Performance suggestions
  if (code.includes('.apply(') && code.includes('lambda')) {
    suggestions.push('Consider using vectorized operations instead of apply() with lambda for better performance');
  }

  if (code.includes('pd.concat') && code.includes('for ')) {
    suggestions.push('Avoid concatenating DataFrames in loops; collect data first, then concatenate once');
  }

  // Best practices
  if (code.includes('train_test_split') && !code.includes('random_state')) {
    suggestions.push('Set random_state in train_test_split for reproducible results');
  }

  if (code.includes('RandomForest') && !code.includes('random_state')) {
    suggestions.push('Set random_state in RandomForest for reproducible results');
  }

  // Security suggestions
  if (code.includes('eval(') || code.includes('exec(')) {
    suggestions.push('⚠️ Avoid using eval() or exec() for security reasons');
  }

  // Notebook-specific suggestions based on metadata
  if (metadata.notebookType === 'competition') {
    suggestions.push('Consider ensemble methods for better competition performance');
    suggestions.push('Implement robust cross-validation strategy');
  }

  if (metadata.cellCount > 20) {
    suggestions.push('Consider organizing code into functions for better maintainability');
  }

  return suggestions;
}

// Code quality assessment
function assessCodeQuality(code) {
  let score = 100;
  const issues = [];

  // Check for imports at the top
  const lines = code.split('\n');
  const importLines = lines.filter(line => line.trim().startsWith('import') || line.trim().startsWith('from'));
  const firstImportIndex = lines.findIndex(line => line.trim().startsWith('import') || line.trim().startsWith('from'));
  const lastImportIndex = lines.map((line, index) => line.trim().startsWith('import') || line.trim().startsWith('from') ? index : -1).filter(i => i !== -1).pop();

  if (importLines.length > 0 && lastImportIndex - firstImportIndex > importLines.length) {
    score -= 10;
    issues.push('Imports should be grouped at the top of the file');
  }

  // Check for long lines
  const longLines = lines.filter(line => line.length > 100);
  if (longLines.length > 0) {
    score -= Math.min(20, longLines.length * 2);
    issues.push(`${longLines.length} lines exceed 100 characters`);
  }

  // Check for magic numbers
  const magicNumbers = code.match(/\b\d{2,}\b/g);
  if (magicNumbers && magicNumbers.length > 3) {
    score -= 10;
    issues.push('Consider defining constants for magic numbers');
  }

  // Check for proper error handling
  if (code.includes('pd.read_csv') && !code.includes('try:')) {
    score -= 15;
    issues.push('Consider adding error handling for file operations');
  }

  return { score: Math.max(0, score), issues };
}

// Performance analysis
function analyzePerformance(code) {
  let score = 100;
  const bottlenecks = [];

  // Check for inefficient operations
  if (code.includes('.apply(') && code.includes('lambda')) {
    score -= 20;
    bottlenecks.push('Using apply() with lambda - consider vectorized operations');
  }

  if (code.includes('pd.concat') && code.includes('for ')) {
    score -= 30;
    bottlenecks.push('Concatenating DataFrames in loop - collect data first');
  }

  if (code.includes('iterrows()')) {
    score -= 25;
    bottlenecks.push('Using iterrows() - very slow, consider vectorized operations');
  }

  if (code.includes('.loc[') && code.includes('for ')) {
    score -= 15;
    bottlenecks.push('Using .loc in loop - consider batch operations');
  }

  return { score: Math.max(0, score), bottlenecks };
}

// Security check
function checkSecurity(code) {
  let score = 100;
  const issues = [];

  if (code.includes('eval(') || code.includes('exec(')) {
    score -= 50;
    issues.push('Using eval() or exec() - potential security risk');
  }

  if (code.includes('subprocess') || code.includes('os.system')) {
    score -= 30;
    issues.push('Using system commands - potential security risk');
  }

  if (code.includes('pickle.load') && !code.includes('# trusted')) {
    score -= 20;
    issues.push('Loading pickle files - ensure source is trusted');
  }

  return { score: Math.max(0, score), issues };
}

// Data flow analysis
function analyzeDataFlow(code) {
  const steps = [];
  const lines = code.split('\n');

  lines.forEach((line, index) => {
    const trimmed = line.trim();

    if (trimmed.includes('pd.read_csv') || trimmed.includes('pd.read_excel')) {
      steps.push({ step: index + 1, type: 'data_loading', description: 'Data loading' });
    }

    if (trimmed.includes('fillna') || trimmed.includes('dropna')) {
      steps.push({ step: index + 1, type: 'data_cleaning', description: 'Data cleaning' });
    }

    if (trimmed.includes('train_test_split')) {
      steps.push({ step: index + 1, type: 'data_splitting', description: 'Data splitting' });
    }

    if (trimmed.includes('.fit(')) {
      steps.push({ step: index + 1, type: 'model_training', description: 'Model training' });
    }

    if (trimmed.includes('.predict(')) {
      steps.push({ step: index + 1, type: 'prediction', description: 'Making predictions' });
    }

    if (trimmed.includes('plt.') || trimmed.includes('sns.')) {
      steps.push({ step: index + 1, type: 'visualization', description: 'Data visualization' });
    }
  });

  return { steps };
}

// Keep original functions for backward compatibility
function detectPatterns(code) {
  return detectAdvancedPatterns(code);
}

function getBasicSuggestions(code) {
  return getAdvancedSuggestions(code);
}
