<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kaggle Assistant Settings</title>
    <style>
        body {
            width: 400px;
            padding: 20px;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .section h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #f0f0f0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        input[type="password"], input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input[type="password"]::placeholder,
        input[type="text"]::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .button {
            background: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background-color 0.2s;
        }
        
        .button:hover {
            background: #45a049;
        }
        
        .button.secondary {
            background: #f44336;
        }
        
        .button.secondary:hover {
            background: #da190b;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 13px;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .status.info {
            background: rgba(33, 150, 243, 0.2);
            border: 1px solid #2196F3;
            color: #2196F3;
        }
        
        .warning {
            background: rgba(255, 152, 0, 0.1);
            border: 1px solid #FF9800;
            color: #FF9800;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 13px;
        }
        
        .warning strong {
            display: block;
            margin-bottom: 5px;
        }
        
        .feature-list {
            margin: 15px 0;
        }
        
        .feature-list li {
            margin: 5px 0;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .toggle-password {
            position: relative;
        }
        
        .toggle-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            font-size: 12px;
        }
        
        .api-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #f44336;
        }
        
        .status-dot.connected {
            background: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚙️ Settings</h1>
    </div>
    
    <div class="warning">
        <strong>🔒 Security Notice</strong>
        Your API key is stored locally in your browser and never transmitted except to OpenAI's servers for analysis.
    </div>
    
    <div class="section">
        <h3>🤖 AI Enhancement (Optional)</h3>
        <p style="font-size: 13px; margin-bottom: 15px; color: rgba(255, 255, 255, 0.8);">
            Connect your OpenAI API key to get AI-powered code analysis and suggestions.
        </p>
        
        <div class="form-group">
            <label for="apiKey">OpenAI API Key:</label>
            <div class="toggle-password">
                <input type="password" id="apiKey" placeholder="sk-proj-..." />
                <button type="button" class="toggle-btn" onclick="togglePassword()">👁️</button>
            </div>
        </div>
        
        <div class="api-status">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">Not connected</span>
        </div>
        
        <div style="margin-top: 15px;">
            <button class="button" onclick="saveSettings()">💾 Save</button>
            <button class="button secondary" onclick="clearSettings()">🗑️ Clear</button>
            <button class="button" onclick="testConnection()" style="background: #2196F3;">🔍 Test</button>
        </div>
        
        <div id="statusMessage"></div>
        
        <div class="feature-list">
            <strong>AI Features:</strong>
            <ul>
                <li>Advanced code analysis and suggestions</li>
                <li>Context-aware optimization recommendations</li>
                <li>Intelligent error detection and solutions</li>
                <li>Personalized coding best practices</li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h3>🎛️ Extension Settings</h3>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="autoSuggestions" checked> 
                Enable auto-suggestions while typing
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="keyboardShortcuts" checked> 
                Enable keyboard shortcuts
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="contextMenus" checked> 
                Enable right-click context menus
            </label>
        </div>
        
        <div class="form-group">
            <label for="suggestionDelay">Auto-suggestion delay (seconds):</label>
            <input type="number" id="suggestionDelay" min="1" max="10" value="2" />
        </div>
        
        <button class="button" onclick="saveExtensionSettings()">💾 Save Settings</button>
    </div>
    
    <div class="section">
        <h3>📊 Usage Statistics</h3>
        <div id="usageStats">
            <p>Suggestions generated: <span id="suggestionsCount">0</span></p>
            <p>Analyses performed: <span id="analysesCount">0</span></p>
            <p>Errors debugged: <span id="errorsCount">0</span></p>
        </div>
        <button class="button secondary" onclick="resetStats()">🔄 Reset Stats</button>
    </div>
    
    <script src="settings.js"></script>
</body>
</html>
