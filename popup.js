// Popup script for Kaggle Assistant
document.addEventListener('DOMContentLoaded', function() {
    const statusDot = document.getElementById('status-dot');
    const statusText = document.getElementById('status-text');
    const toggleSidebarBtn = document.getElementById('toggle-sidebar');
    const openSettingsBtn = document.getElementById('open-settings');
    const openKaggleBtn = document.getElementById('open-kaggle');
    const refreshExtensionBtn = document.getElementById('refresh-extension');

    // Check if current tab is a Kaggle notebook
    checkKaggleStatus();

    // Event listeners
    toggleSidebarBtn.addEventListener('click', toggleSidebar);
    openSettingsBtn.addEventListener('click', openSettings);
    openKaggleBtn.addEventListener('click', openKaggle);
    refreshExtensionBtn.addEventListener('click', refreshExtension);

    function checkKaggleStatus() {
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            const currentTab = tabs[0];
            
            if (currentTab.url && currentTab.url.includes('kaggle.com/code/')) {
                // On a Kaggle notebook page
                statusDot.className = 'status-dot active';
                statusText.textContent = 'Active on Kaggle notebook';
                toggleSidebarBtn.disabled = false;
            } else if (currentTab.url && currentTab.url.includes('kaggle.com')) {
                // On Kaggle but not a notebook
                statusDot.className = 'status-dot inactive';
                statusText.textContent = 'On Kaggle - navigate to a notebook';
                toggleSidebarBtn.disabled = true;
            } else {
                // Not on Kaggle
                statusDot.className = 'status-dot inactive';
                statusText.textContent = 'Not on Kaggle - click "Open Kaggle"';
                toggleSidebarBtn.disabled = true;
            }
        });
    }

    function toggleSidebar() {
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'toggleSidebar' }, function(response) {
                if (chrome.runtime.lastError) {
                    // Content script not loaded, inject it
                    chrome.scripting.executeScript({
                        target: { tabId: tabs[0].id },
                        files: ['content.js']
                    }, function() {
                        // Try again after injection
                        setTimeout(() => {
                            chrome.tabs.sendMessage(tabs[0].id, { action: 'toggleSidebar' });
                        }, 100);
                    });
                }
            });
        });
        
        // Close popup after action
        window.close();
    }

    function openSettings() {
        chrome.runtime.openOptionsPage();
        window.close();
    }

    function openKaggle() {
        chrome.tabs.create({ url: 'https://www.kaggle.com/code' });
        window.close();
    }

    function refreshExtension() {
        chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
            chrome.tabs.reload(tabs[0].id);
            window.close();
        });
    }

    // Listen for tab updates to refresh status
    chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
        if (changeInfo.status === 'complete') {
            checkKaggleStatus();
        }
    });
});
