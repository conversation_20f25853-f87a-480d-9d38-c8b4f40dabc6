/* Kaggle Assistant Sidebar Styles */
.kaggle-assistant-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 350px;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  z-index: 10000;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease-in-out;
  overflow-y: auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  gap: 5px;
}

.toggle-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4CAF50;
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.notebook-info {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.clear-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s;
}

.clear-btn:hover {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}

.btn-icon {
  margin-right: 8px;
  font-size: 14px;
}

.placeholder {
  text-align: center;
  padding: 30px 20px;
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.placeholder p {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  margin: 0 0 15px 0;
}

.quick-tips {
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid #2196F3;
}

.quick-tips small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 11px;
}

/* Enhanced suggestion styles */
.context-info {
  background: rgba(33, 150, 243, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 15px;
  border-left: 3px solid #2196F3;
}

.context-info small {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
}

.suggestion-item {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid #4CAF50;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-1px);
}

.suggestion-item.priority-high {
  border-left-color: #FF5722;
  background: rgba(255, 87, 34, 0.1);
}

.suggestion-item.priority-medium {
  border-left-color: #FF9800;
  background: rgba(255, 152, 0, 0.1);
}

.suggestion-item.priority-low {
  border-left-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.suggestion-desc {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: normal;
}

.priority-badge {
  background: #FF5722;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.insert-btn, .explain-btn {
  background: #2196F3;
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  transition: background-color 0.2s;
}

.insert-btn:hover {
  background: #1976D2;
}

.explain-btn {
  background: #9C27B0;
}

.explain-btn:hover {
  background: #7B1FA2;
}

.explanation {
  margin-top: 10px;
  padding: 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border-left: 3px solid #9C27B0;
  font-size: 12px;
  line-height: 1.4;
}

.suggestion-tips {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 3px solid #607D8B;
}

.suggestion-tips h6 {
  margin: 0 0 10px 0;
  color: #607D8B;
  font-size: 13px;
}

.suggestion-tips ul {
  margin: 0;
  padding-left: 20px;
}

.suggestion-tips li {
  font-size: 12px;
  margin: 5px 0;
  color: rgba(255, 255, 255, 0.8);
}

kbd {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: monospace;
}

/* Error debugging styles */
.error-category {
  margin-bottom: 20px;
}

.error-category h6 {
  color: #FF9800;
  margin: 0 0 10px 0;
  font-size: 14px;
  border-bottom: 1px solid rgba(255, 152, 0, 0.3);
  padding-bottom: 5px;
}

.error-item.detected {
  border-left-color: #f44336;
  background: rgba(244, 67, 54, 0.1);
}

.error-solution {
  margin-top: 8px;
  padding: 8px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 4px;
  font-size: 12px;
}

.error-fix {
  margin-top: 10px;
  padding: 10px;
  background: rgba(33, 150, 243, 0.1);
  border-radius: 4px;
}

.debug-tools {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 3px solid #9E9E9E;
}

.debug-tools h6 {
  color: #9E9E9E;
  margin: 0 0 10px 0;
  font-size: 13px;
}

/* Format section styles */
.format-section {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 3px solid #673AB7;
}

.format-section h6 {
  color: #673AB7;
  margin: 0 0 10px 0;
  font-size: 14px;
}

/* Analysis section styles */
.analysis-section {
  margin-bottom: 15px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border-left: 3px solid #00BCD4;
}

.analysis-section h6 {
  color: #00BCD4;
  margin: 0 0 8px 0;
  font-size: 13px;
}

.pattern-item, .suggestion-item {
  margin: 5px 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.pattern-item {
  padding: 4px 0;
}

/* Notification animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Cell highlighting */
.cell.assistant-active,
.code-cell.assistant-active {
  box-shadow: 0 0 0 2px #4CAF50 !important;
  border-radius: 4px;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* Context menu styles */
.cell-context-menu {
  background: #333;
  border: 1px solid #555;
  border-radius: 6px;
  padding: 8px 0;
  min-width: 180px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 13px;
  color: white;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background: #4CAF50;
}

/* Auto-suggestion styles */
.auto-suggestion {
  background: #4CAF50;
  color: white;
  padding: 12px;
  border-radius: 6px;
  max-width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: slideInRight 0.3s ease;
}

.suggestion-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 13px;
}

.suggestion-code {
  background: rgba(0, 0, 0, 0.2);
  padding: 6px 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 11px;
  margin: 8px 0;
}

.auto-suggestion button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  margin-right: 8px;
  margin-top: 8px;
}

.auto-suggestion button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Assistant indicator styles */
.assistant-indicator {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: rgba(76, 175, 80, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  cursor: pointer;
  z-index: 1000;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.assistant-indicator:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Quick help styles */
.quick-help {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.help-section {
  margin-bottom: 15px;
}

.help-section h6 {
  color: #2196F3;
  margin: 0 0 8px 0;
  font-size: 13px;
  border-bottom: 1px solid rgba(33, 150, 243, 0.3);
  padding-bottom: 4px;
}

.help-section ul {
  margin: 0;
  padding-left: 20px;
}

.help-section li {
  font-size: 12px;
  margin: 4px 0;
  color: rgba(255, 255, 255, 0.9);
}

/* Minimized sidebar styles */
.kaggle-assistant-sidebar.minimized {
  width: 60px !important;
}

.kaggle-assistant-sidebar.minimized .sidebar-content {
  display: none;
}

.kaggle-assistant-sidebar.minimized .sidebar-header h3 {
  display: none;
}

.kaggle-assistant-sidebar.minimized .header-controls {
  justify-content: center;
  width: 100%;
}

/* Enhanced responsive design */
@media (max-width: 1600px) {
  .kaggle-assistant-sidebar {
    width: 340px;
  }
}

@media (max-width: 1400px) {
  .kaggle-assistant-sidebar {
    width: 320px;
  }

  .auto-suggestion {
    max-width: 250px;
  }
}

@media (max-width: 1200px) {
  .kaggle-assistant-sidebar {
    width: 300px;
  }

  .auto-suggestion {
    max-width: 220px;
    right: 10px;
  }
}

@media (max-width: 768px) {
  .kaggle-assistant-sidebar {
    width: 280px;
  }

  .auto-suggestion {
    max-width: 200px;
    right: 5px;
    top: 80px;
  }

  .cell-context-menu {
    min-width: 150px;
  }

  .context-menu-item {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .kaggle-assistant-sidebar {
    width: 100vw;
    height: 100vh;
  }

  .auto-suggestion {
    position: relative;
    top: auto;
    right: auto;
    margin: 10px;
    max-width: none;
  }
}

.sidebar-content {
  padding: 20px;
}

.section {
  margin-bottom: 25px;
}

.section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #f0f0f0;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
}

.action-btn {
  display: block;
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  text-align: left;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.action-btn:active {
  transform: translateY(0);
}

.results-container {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
  min-height: 100px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  margin: 0;
}

.loading {
  text-align: center;
  color: #f0f0f0;
  font-style: italic;
}

.error {
  color: #ff6b6b;
  text-align: center;
  font-weight: 500;
}

.analysis-result h5,
.viz-suggestions h5,
.data-summary h5,
.code-suggestions h5,
.optimization-tips h5,
.comment-templates h5,
.imports-template h5,
.debug-help h5,
.performance-tips h5 {
  margin: 0 0 10px 0;
  color: #f0f0f0;
  font-size: 14px;
  font-weight: 600;
}

.analysis-result p {
  margin: 5px 0;
  font-size: 13px;
}

.suggestions,
.patterns {
  margin-top: 10px;
}

.suggestions h6,
.patterns h6 {
  margin: 5px 0;
  color: #e0e0e0;
  font-size: 12px;
}

.suggestions li,
.patterns li {
  font-size: 12px;
  margin: 3px 0;
  list-style-type: none;
  padding-left: 15px;
  position: relative;
}

.suggestions li:before,
.patterns li:before {
  content: "•";
  color: #4CAF50;
  position: absolute;
  left: 0;
}

.suggestion-item,
.template-item,
.error-item {
  margin-bottom: 12px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border-left: 3px solid #4CAF50;
}

.suggestion-item strong,
.template-item strong,
.error-item strong {
  color: #4CAF50;
  display: block;
  margin-bottom: 5px;
  font-size: 13px;
}

.suggestion-item code,
.template-item code {
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #f0f0f0;
}

pre {
  background: rgba(0, 0, 0, 0.4);
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 8px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

pre code {
  color: #f0f0f0;
  font-size: 12px;
  line-height: 1.4;
  font-family: 'Courier New', monospace;
}

.copy-btn {
  background: #4CAF50;
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  margin-top: 8px;
  transition: background-color 0.2s;
}

.copy-btn:hover {
  background: #45a049;
}

.optimization-tips ul,
.performance-tips ul {
  margin: 10px 0;
  padding-left: 20px;
}

.optimization-tips li,
.performance-tips li {
  margin: 8px 0;
  font-size: 13px;
  line-height: 1.4;
}

/* Scrollbar styling */
.kaggle-assistant-sidebar::-webkit-scrollbar,
.results-container::-webkit-scrollbar {
  width: 6px;
}

.kaggle-assistant-sidebar::-webkit-scrollbar-track,
.results-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.kaggle-assistant-sidebar::-webkit-scrollbar-thumb,
.results-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.kaggle-assistant-sidebar::-webkit-scrollbar-thumb:hover,
.results-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Enhanced UI Components */
.notebook-type-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #4CAF50;
}

.notebook-type-section h4 {
  color: #4CAF50;
  margin-bottom: 12px;
}

.strategy-item {
  margin-bottom: 15px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border-left: 3px solid #2196F3;
}

.strategy-item strong {
  color: #2196F3;
  display: block;
  margin-bottom: 5px;
  font-size: 13px;
}

.strategy-item p {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

.competition-analysis,
.dataset-exploration,
.data-quality,
.feature-analysis,
.notebook-structure,
.documentation-templates {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.structure-section ol {
  margin: 10px 0;
  padding-left: 20px;
}

.structure-section li {
  margin: 8px 0;
  font-size: 13px;
  line-height: 1.4;
}

.structure-section strong {
  color: #4CAF50;
}

.markdown-templates,
.doc-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.doc-section strong {
  color: #FF9800;
  display: block;
  margin-bottom: 8px;
  font-size: 13px;
}

/* Loading and status indicators */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.success {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #4CAF50;
}

.warning {
  color: #FF9800;
  background: rgba(255, 152, 0, 0.1);
  padding: 10px;
  border-radius: 6px;
  border-left: 3px solid #FF9800;
}

/* Enhanced button styles */
.action-btn {
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn.active {
  background: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  color: #4CAF50;
}

/* Collapsible sections */
.section.collapsible .section-header {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.section.collapsible .section-header::after {
  content: '▼';
  font-size: 12px;
  transition: transform 0.3s ease;
}

.section.collapsible.collapsed .section-header::after {
  transform: rotate(-90deg);
}

.section.collapsible.collapsed .section-content {
  display: none;
}

/* Tooltip system */
.tooltip {
  position: relative;
  cursor: help;
}

.tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 1000;
}

.tooltip:hover::before {
  opacity: 1;
}

/* Code highlighting */
.code-highlight {
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
}

/* Progress indicators */
.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin: 10px 0;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #2196F3);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
  .kaggle-assistant-sidebar {
    width: 320px;
  }
}

@media (max-width: 1200px) {
  .kaggle-assistant-sidebar {
    width: 300px;
  }

  .sidebar-content {
    padding: 18px;
  }
}

@media (max-width: 768px) {
  .kaggle-assistant-sidebar {
    width: 280px;
  }

  .sidebar-content {
    padding: 15px;
  }

  .action-btn {
    padding: 10px 12px;
    font-size: 13px;
  }

  .section h4 {
    font-size: 15px;
  }

  pre code {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .kaggle-assistant-sidebar {
    width: 100vw;
    height: 100vh;
  }

  .sidebar-header {
    padding: 15px;
  }

  .sidebar-content {
    padding: 12px;
  }

  .action-btn {
    padding: 8px 10px;
    font-size: 12px;
  }
}

/* Animation for sidebar entrance */
@keyframes slideIn {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.kaggle-assistant-sidebar {
  animation: slideIn 0.3s ease-out;
}
